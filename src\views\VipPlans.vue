<template>
  <div class="vip-plans-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-star-on"></i>
          选择您的VIP套餐
        </h1>
        <p class="page-subtitle">解锁更多AI功能，享受更好的服务体验</p>
      </div>
    </div>

    <!-- 当前VIP状态 -->
    <div v-if="isVip" class="current-status">
      <div class="status-card">
        <div class="status-info">
          <div class="status-badge" :class="vipLevel">
            <i class="el-icon-crown"></i>
            {{ currentVipPlan.name }}
          </div>
          <div class="expire-info">
            <span v-if="vipExpireTime">
              到期时间：{{ formatDate(vipExpireTime) }}
            </span>
          </div>
        </div>
        <div class="status-actions">
          <el-button type="primary" @click="showRenewalDialog = true">
            续费
          </el-button>
        </div>
      </div>
    </div>

    <!-- 免费用户升级提示 -->
    <div v-else class="free-user-promotion">
      <div class="promotion-card">
        <div class="promotion-content">
          <div class="promotion-icon">
            <i class="el-icon-star-on"></i>
          </div>
          <div class="promotion-text">
            <h3>您当前是免费用户</h3>
            <p>升级VIP会员，解锁更多强大功能，享受更好的AI体验</p>
          </div>
        </div>
        <div class="promotion-benefits">
          <div class="benefit-item">
            <i class="el-icon-check"></i>
            <span>每日对话次数大幅提升</span>
          </div>
          <div class="benefit-item">
            <i class="el-icon-check"></i>
            <span>使用更强大的AI模型</span>
          </div>
          <div class="benefit-item">
            <i class="el-icon-check"></i>
            <span>优先响应速度</span>
          </div>
          <div class="benefit-item">
            <i class="el-icon-check"></i>
            <span>更长的消息保存时间</span>
          </div>
        </div>
      </div>
    </div>

    <!-- VIP套餐卡片 -->
    <div class="plans-grid" v-loading="loading" element-loading-text="正在加载VIP套餐信息...">
      <!-- 空状态提示 -->
      <div v-if="!loading && vipPlans.length === 0" class="empty-state">
        <div class="empty-icon">
          <i class="el-icon-warning"></i>
        </div>
        <div class="empty-text">
          <h3>暂无VIP套餐信息</h3>
          <p>请稍后重试或联系客服</p>
        </div>
        <el-button type="primary" @click="initVipPlans">重新加载</el-button>
      </div>

      <div
        v-for="plan in vipPlans"
        :key="plan.id"
        class="plan-card"
        :class="{
          'current': plan.id === vipLevel,
          'selected': selectedPlanId === plan.id && plan.id !== vipLevel,
          'recommended': plan.isRecommended,
          'premium': plan.id === 'super_vip'
        }"
        @click="selectPlanCard(plan)"
      >
        <!-- 推荐标签 -->
        <div v-if="plan.isRecommended" class="recommended-badge">
          推荐
        </div>

        <!-- 超级VIP标签 -->
        <div v-if="plan.id === 'super_vip'" class="super-vip-badge">
          <i class="el-icon-star-on"></i>
          超级VIP
        </div>

        <!-- 套餐头部 -->
        <div class="plan-header">
          <div class="plan-icon">
            <i :class="getPlanIcon(plan.id)"></i>
          </div>
          <h3 class="plan-name">{{ plan.name }}</h3>
          <div class="plan-price">
            <template v-if="plan.id === 'free'">
              <div class="price-main">
                <span class="price-amount free">免费</span>
              </div>
            </template>
            <template v-else>
              <div class="price-main">
                <span class="price-amount">¥{{ (plan.monthlyPrice / 100).toFixed(0) }}</span>
                <span class="price-period">/月</span>
              </div>
              <div class="price-yearly">
                <span class="yearly-amount">¥{{ (plan.yearlyPrice / 100).toFixed(0) }}</span>
                <span class="yearly-period">/年</span>
                <span class="price-save">省{{ Math.round((1 - plan.yearlyPrice / (plan.monthlyPrice * 12)) * 100) }}%</span>
              </div>
            </template>
          </div>
        </div>

        <!-- 套餐特性 -->
        <div class="plan-features">
          <ul class="features-list">
            <li v-for="feature in plan.features" :key="feature" class="feature-item">
              <i class="el-icon-check"></i>
              <span>{{ feature.feature }}</span>
            </li>
          </ul>
        </div>

        <!-- 套餐操作 -->
        <div class="plan-actions">
          <div class="button-slot-1">
            <template v-if="plan.id === 'free'">
              <!-- 免费套餐第一个按钮位置为空 -->
            </template>
            <template v-else-if="plan.id === vipLevel">
              <!-- 当前套餐第一个按钮位置为空 -->
            </template>
            <template v-else>
              <el-button
                type="primary"
                :class="{ 'premium-btn': plan.id === 'super_vip' }"
                @click="selectPlan(plan, 'monthly')"
              >
                月付购买
              </el-button>
            </template>
          </div>

          <div class="button-slot-2">
            <template v-if="plan.id === 'free'">
              <el-button
                v-if="vipLevel !== 'free'"
                type="info"
                disabled
              >
                当前套餐
              </el-button>
              <el-button
                v-else
                type="info"
                disabled
              >
                当前使用
              </el-button>
            </template>
            <template v-else-if="plan.id === vipLevel">
              <el-button
                type="success"
                disabled
              >
                当前套餐
              </el-button>
            </template>
            <template v-else>
              <el-button
                type="primary"
                plain
                @click="selectPlan(plan, 'yearly')"
              >
                年付购买
              </el-button>
            </template>
          </div>
        </div>
      </div>
    </div>

    <!-- 购买确认对话框 -->
    <el-dialog
      title="确认购买"
      :visible.sync="showPurchaseDialog"
      width="520px"
      :close-on-click-modal="false"
      custom-class="purchase-modal"
      @close="closePurchaseDialog"
    >
      <div v-if="selectedPlan" class="purchase-dialog">
        <!-- 套餐信息卡片 -->
        <div class="purchase-info-card">
          <div class="plan-info-header">
            <!-- <div class="plan-icon-small">
              <i :class="selectedPlan.icon"></i>
            </div> -->
            <div class="plan-details">
              <h3 class="plan-title">{{ selectedPlan.name }}</h3>
              <p class="plan-duration">购买时长：{{ selectedDuration === 'monthly' ? '1个月' : '1年' }}</p>
            </div>
          </div>
          <div class="price-section">
            <div class="price-label">支付金额：</div>
            <div class="price-amount">¥{{ getPurchasePrice() }}</div>
          </div>
        </div>

        <!-- 支付方式选择 -->
        <div class="payment-section">
          <h4 class="section-title">选择支付方式：</h4>
          <div class="payment-options" v-loading="loading" element-loading-text="正在加载支付方式...">
            <!-- 动态渲染支付方式 -->
            <div
              v-for="method in paymentMethods"
              :key="method.id"
              class="payment-option"
              :class="{
                'selected': selectedPaymentMethod === method.id,
                'recommended': method.isRecommended,
                'disabled': method.id === 'balance' && balance < getPurchasePrice() * 100
              }"
              @click="selectPaymentMethod(method.id)"
            >
              <!-- 推荐标签 -->
              <div v-if="method.isRecommended" class="recommended-badge">
                推荐
              </div>
              <div class="payment-method-content">
                <div class="payment-icon-wrapper">
                  <!-- 如果有图片则显示图片，否则显示图标 -->
                  <img
                    v-if="method.image"
                    :src="method.image"
                    :alt="method.name"
                    class="payment-method-img"
                  >
                  <div
                    v-else
                    class="payment-icon"
                    :class="{
                      'balance': method.id === 'balance',
                      'disabled': method.id === 'balance' && balance < getPurchasePrice() * 100
                    }"
                  >
                    <i :class="method.icon || 'el-icon-wallet'"></i>
                  </div>
                </div>
                <div class="payment-info">
                  <span class="payment-name">{{ method.name }}</span>
                  <span
                    v-if="method.id === 'balance'"
                    class="balance-info"
                    :class="{ insufficient: balance < getPurchasePrice() * 100 }"
                  >
                    (¥{{ balanceYuan }})
                  </span>
                  <span v-else class="payment-desc">{{ method.description }}</span>
                </div>
                <div class="payment-check-icon">
                  <i class="el-icon-check"></i>
                </div>
              </div>
            </div>

            <!-- 如果没有支付方式数据，显示空状态 -->
            <div v-if="!loading && paymentMethods.length === 0" class="empty-payment-methods">
              <div class="empty-icon">
                <i class="el-icon-warning"></i>
              </div>
              <div class="empty-text">
                <p>暂无可用的支付方式</p>
                <el-button type="primary" size="small" @click="initPaymentMethods">重新加载</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button size="large" @click="showPurchaseDialog = false">取消</el-button>
        <el-button
          type="primary"
          size="large"
          :loading="purchasing"
          @click="confirmPurchase"
          class="confirm-pay-btn"
        >
          <i class="el-icon-check" v-if="!purchasing"></i>
          {{ purchasing ? '处理中...' : '确认支付' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 续费对话框 -->
    <el-dialog
      title="VIP续费"
      :visible.sync="showRenewalDialog"
      width="400px"
    >
      <div class="renewal-dialog">
        <p>当前套餐：{{ currentVipPlan.name }}</p>
        <p>到期时间：{{ formatDate(vipExpireTime) }}</p>
        
        <div class="renewal-options">
          <el-button 
            type="primary" 
            @click="selectPlan(currentVipPlan, 'monthly')"
          >
            续费1个月
          </el-button>
          <el-button 
            type="primary" 
            @click="selectPlan(currentVipPlan, 'yearly')"
          >
            续费1年
          </el-button>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
import { getVipPlans,getPaymentMethods } from '@/api/vip'

export default {
  name: 'VipPlans',
  data() {
    return {
      showPurchaseDialog: false,
      showRenewalDialog: false,
      selectedPlan: null,
      selectedPlanId: null, // 用于跟踪用户选中的套餐卡片
      selectedDuration: 'monthly',
      selectedPaymentMethod: 'alipay',
      purchasing: false,
      loading: false,
      vipPlans: [], // 直接在组件中管理VIP套餐数据
      paymentMethods:[]
    }
  },
  computed: {
    ...mapGetters('user', [
      'vipLevel',
      'isVip',
      'vipExpireTime',
      'balance',
      'balanceYuan'
    ]),

    // 根据当前VIP等级获取当前套餐信息
    currentVipPlan() {
      return this.vipPlans.find(plan => plan.id === this.vipLevel) || this.vipPlans[0] || {}
    }
  },
  mounted(){
    this.initVipPlans();
  },
  methods: {
    //初始化vip套餐以及特性
    async initVipPlans(){
      try {
        this.loading = true
        const plans = await getVipPlans()
        this.vipPlans = plans
        console.log("VIP套餐数据获取成功", plans)
      } catch (error) {
        console.error("获取VIP套餐失败:", error)
        this.$message.error('获取VIP套餐信息失败，请刷新重试')
      } finally {
        this.loading = false
      }
    },
    //支付选择方式列表
    async initPaymentMethods(){
      try {
        this.loading = true
        const res = await getPaymentMethods()
        this.paymentMethods = res.data || res
        console.log("支付方式列表获取成功", this.paymentMethods)
      } catch (error) {
        console.error("获取支付方式失败:", error)
        this.$message.error('获取支付方式失败，请刷新重试')
      } finally {
        this.loading = false
      }
    },

    ...mapActions('user', ['purchaseVip']),

    getPlanIcon(planId) {
      const icons = {
        free: 'el-icon-user',
        vip: 'el-icon-star-on',
        super_vip: 'el-icon-crown'
      }
      return icons[planId] || 'el-icon-user'
    },

    // 选中套餐卡片（用于显示选中状态）
    selectPlanCard(plan) {
      // 如果是当前套餐，不改变选中状态
      if (plan.id === this.vipLevel) {
        return
      }
      // 切换选中状态
      this.selectedPlanId = this.selectedPlanId === plan.id ? null : plan.id
      // 如果选中了套餐，初始化支付方式
      if (this.selectedPlanId) {
        this.initPaymentMethods();
      }
    },

    async selectPlan(plan, duration) {
      this.selectedPlan = plan
      this.selectedPlanId = plan.id // 同时设置选中状态
      this.selectedDuration = duration
      this.showRenewalDialog = false

      // 初始化支付方式
      await this.initPaymentMethods()

      // 设置默认支付方式为第一个推荐的支付方式，如果没有推荐的则选择第一个
      if (this.paymentMethods.length > 0) {
        const recommendedMethod = this.paymentMethods.find(method => method.recommended)
        this.selectedPaymentMethod = recommendedMethod ? recommendedMethod.id : this.paymentMethods[0].id
      } else {
        this.selectedPaymentMethod = 'alipay' // 兜底选择
      }

      this.showPurchaseDialog = true
    },

    selectPaymentMethod(methodId) {
      // 检查余额支付是否可用
      if (methodId === 'balance' && this.balance < this.getPurchasePrice() * 100) {
        this.$message.warning('余额不足，请选择其他支付方式或先充值')
        return
      }

      // 检查支付方式是否可用
      const method = this.paymentMethods.find(m => m.id === methodId)
      if (!method) {
        this.$message.error('支付方式不可用')
        return
      }

      // 如果支付方式被禁用，不允许选择
      if (method.disabled) {
        this.$message.warning(method.disabledReason || '该支付方式暂时不可用')
        return
      }

      this.selectedPaymentMethod = methodId
    },
    
    getPurchasePrice() {
      if (!this.selectedPlan) return 0
      const price = this.selectedDuration === 'monthly' 
        ? this.selectedPlan.monthlyPrice 
        : this.selectedPlan.yearlyPrice
      return (price / 100).toFixed(2)
    },
    
    async confirmPurchase() {
      this.purchasing = true
      
      try {
        const result = await this.purchaseVip({
          planId: this.selectedPlan.id,
          duration: this.selectedDuration === 'monthly' ? 30 : 365,
          paymentMethod: this.selectedPaymentMethod
        })
        
        if (result.success) {
          this.$message.success('购买成功！')
          this.showPurchaseDialog = false
          this.selectedPlan = null
          this.selectedPlanId = null // 清除选中状态
        } else {
          this.$message.error(result.message)
        }
      } catch (error) {
        this.$message.error('购买失败，请重试')
      } finally {
        this.purchasing = false
      }
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      return new Date(dateString).toLocaleDateString('zh-CN')
    },

    // 关闭购买对话框时清除选中状态
    closePurchaseDialog() {
      this.showPurchaseDialog = false
      this.selectedPlan = null
      this.selectedPlanId = null
    }
  }
}
</script>

<style lang="scss" scoped>
.vip-plans-container {
  width: 95%;
  max-width: 2000px;
  margin: 0 auto;
  padding: 30px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  
  .page-title {
    font-size: 2.5rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
    
    i {
      color: #f39c12;
      margin-right: 10px;
    }
  }
  
  .page-subtitle {
    font-size: 1.1rem;
    color: var(--text-secondary);
  }
}

.current-status {
  margin-bottom: 30px;
  
  .status-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  .status-badge {
    display: flex;
    align-items: center;
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 5px;

    i {
      margin-right: 8px;
    }
  }
}

// 免费用户升级提示样式
.free-user-promotion {
  margin-bottom: 30px;

  .promotion-card {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
    border-radius: 16px;
    padding: 24px;
    color: white;
    box-shadow: 0 8px 32px rgba(243, 156, 18, 0.3);
  }

  .promotion-content {
    display: flex;
    align-items: center;
    margin-bottom: 20px;

    .promotion-icon {
      width: 60px;
      height: 60px;
      background: rgba(255, 255, 255, 0.2);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 20px;

      i {
        font-size: 24px;
        color: white;
      }
    }

    .promotion-text {
      flex: 1;

      h3 {
        margin: 0 0 8px 0;
        font-size: 1.4rem;
        font-weight: 600;
      }

      p {
        margin: 0;
        font-size: 1rem;
        opacity: 0.9;
        line-height: 1.5;
      }
    }
  }

  .promotion-benefits {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 16px;

    .benefit-item {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 0.9rem;

      i {
        color: #27ae60;
        font-weight: bold;
        background: white;
        border-radius: 50%;
        width: 16px;
        height: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
      }
    }
  }
}

.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
  padding: 0;
  min-height: 200px;
  align-items: stretch;

  // 响应式设计
  @media (max-width: 1200px) {
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 20px;
  }

  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 20px;
  }
}

.plan-card {
  background: var(--bg-primary);
  border: 2px solid var(--border-color);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  transition: all 0.3s ease;
  min-width: 320px;
  height: 550px;
  display: flex;
  flex-direction: column;

  &:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.12);
  }

  &:not(.current) {
    cursor: pointer;

    &:hover {
      border-color: #52c41a;
    }
  }

  &.current {
    border: 2px solid var(--primary-color);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
  }

  &.selected {
    border: 2px solid #52c41a;
    box-shadow: 0 8px 25px rgba(82, 196, 26, 0.15);
    cursor: pointer;

    &::after {
      content: '✓';
      position: absolute;
      top: 15px;
      left: 15px;
      width: 24px;
      height: 24px;
      background: #52c41a;
      color: white;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      z-index: 10;
    }
  }



  &.recommended {
    .recommended-badge {
      position: absolute;
      top: -12px;
      right: 20px;
      background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
      color: white;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
    }
  }

  &.premium {
    .super-vip-badge {
      position: absolute;
      top: -12px;
      right: 20px;
      background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
      color: white;
      padding: 6px 16px;
      border-radius: 20px;
      font-size: 0.85rem;
      font-weight: 600;
      box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
      display: flex;
      align-items: center;
      gap: 4px;

      i {
        font-size: 0.8rem;
      }
    }
  }
}

.plan-header {
  text-align: center;
  margin-bottom: 20px;
  height: 160px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;

  .plan-icon {
    font-size: 3rem;
    margin-bottom: 12px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;

    i {
      color: var(--primary-color);

      &.el-icon-user {
        color: #95a5a6;
      }

      &.el-icon-star-on {
        color: #f39c12;
      }

      &.el-icon-crown {
        color: #9b59b6;
      }
    }
  }

  .plan-name {
    font-size: 1.4rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 12px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .plan-price {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 6px;
    height: 75px;
  }
}

.price-main {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 3px;
  height: 35px;

  .price-amount {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--primary-color);

    &.free {
      font-size: 1.4rem;
      color: #27ae60;
    }
  }

  .price-period {
    font-size: 0.9rem;
    color: var(--text-secondary);
    font-weight: 500;
  }
}

.price-yearly {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
  opacity: 0.8;
  height: 25px;

  .yearly-amount {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--primary-color);
  }

  .yearly-period {
    font-size: 0.85rem;
    color: var(--text-secondary);
  }

  .price-save {
    background: #e74c3c;
    color: white;
    padding: 1px 6px;
    border-radius: 10px;
    font-size: 0.7rem;
    margin-left: 6px;
    font-weight: 600;
  }
}

.features-list {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
  height: 220px;
  overflow-y: auto;

  .feature-item {
    display: flex;
    align-items: center;
    padding: 8px 0;

    i {
      color: #27ae60;
      margin-right: 8px;
      font-weight: 600;
      font-size: 0.9rem;
      flex-shrink: 0;
    }

    span {
      color: var(--text-primary);
      font-size: 0.9rem;
      line-height: 1.4;
    }
  }
}

.plan-actions {
  display: flex;
  flex-direction: column;
  gap: 10px;
  height: 90px;
  justify-content: flex-end;
  margin-top: auto;

  .button-slot-1,
  .button-slot-2 {
    height: 40px;
    width: 100%;
    display: flex;
    align-items: center;
  }

  .el-button {
    width: 100%;
    height: 40px;
    font-weight: 600;
    border-radius: 8px;
    font-size: 0.9rem;

    &.el-button--primary {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border: none;

      &:hover {
        background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
      }
    }

    &.el-button--info {
      background: #f5f5f5;
      color: #999;
      border: 1px solid #e0e0e0;
    }

    &.el-button--success {
      background: #27ae60;
      border: none;
    }
  }

  .premium-btn {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%) !important;
    border: none;

    &:hover {
      background: linear-gradient(135deg, #8e44ad 0%, #7d3c98 100%) !important;
    }
  }
}

/* 购买对话框样式优化 */
.purchase-modal {
  .el-dialog {
    border-radius: 16px;
    overflow: hidden;
  }

  .el-dialog__header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;
    border-radius: 16px 16px 0 0;

    .el-dialog__title {
      color: white;
      font-size: 1.2rem;
      font-weight: 600;
    }

    .el-dialog__close {
      color: white;
      font-size: 1.2rem;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }
  }

  .el-dialog__body {
    padding: 24px;
    background: #fafbfc;
  }

  .el-dialog__footer {
    padding: 20px 24px 24px;
    border-top: 1px solid var(--border-color);
    background: var(--bg-primary);
  }
}

.purchase-dialog {
  .purchase-info-card {
    background: white;
    border: 1px solid rgba(102, 126, 234, 0.2);
    border-radius: 16px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 4px 16px rgba(102, 126, 234, 0.08);
    position: relative;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-radius: 16px 16px 0 0;
    }

    .plan-info-header {
      display: flex;
      align-items: center;
      margin-bottom: 16px;

      .plan-icon-small {
        width: 40px;
        height: 40px;
        border-radius: 8px;
        background: var(--primary-color);
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 12px;

        i {
          color: white;
          font-size: 1.2rem;
        }
      }

      .plan-details {
        flex: 1;

        .plan-title {
          margin: 0 0 4px 0;
          font-size: 1.1rem;
          font-weight: 600;
          color: var(--text-primary);
        }

        .plan-duration {
          margin: 0;
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
    }

    .price-section {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding-top: 16px;
      border-top: 1px solid rgba(102, 126, 234, 0.15);

      .price-label {
        font-size: 1rem;
        color: var(--text-secondary);
      }

      .price-amount {
        font-size: 1.5rem;
        font-weight: 700;
        color: var(--primary-color);
      }
    }
  }

  .payment-section {
    .section-title {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 700;
      color: #1f2937;
      letter-spacing: -0.025em;
    }

    .payment-options {
      width: 100%;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .payment-option {
        width: 100%;
        flex: none;
        max-width: 100%;
        cursor: pointer;
        transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;

        &.recommended {
          .recommended-badge {
            position: absolute;
            top: -8px;
            right: 16px;
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 700;
            z-index: 10;
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
            letter-spacing: 0.5px;
          }

          /* 推荐但未选中时的样式 - 使用普通样式但保留推荐标签 */
          &:not(.selected) .payment-method-content {
            border-color: #e8eaed;
            background: #ffffff;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
          }
        }

        .payment-method-content {
          display: flex;
          align-items: center;
          padding: 16px 20px;
          border: 2px solid #e8eaed;
          border-radius: 16px;
          transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
          cursor: pointer;
          background: #ffffff;
          box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
          width: 100%;
          height: 72px;
          position: relative;
          box-sizing: border-box;

          .payment-icon-wrapper {
            margin-right: 16px;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            overflow: hidden;
            flex-shrink: 0;
            background: #f5f5f5;
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

            img {
              width: 32px;
              height: 32px;
              object-fit: contain;
              border-radius: 8px;
            }

            .payment-method-img {
              width: 32px;
              height: 32px;
              object-fit: contain;
              border-radius: 8px;
            }

            .payment-icon {
              width: 40px;
              height: 40px;
              border-radius: 12px;
              display: flex;
              align-items: center;
              justify-content: center;

              &.balance {
                background: linear-gradient(135deg, #ff9500 0%, #ff6b00 100%);
                color: white;
                font-size: 1.3rem;

                &.disabled {
                  background: #e0e0e0;
                  color: #9e9e9e;
                }
              }
            }
          }

          .payment-info {
            display: flex;
            flex-direction: column;
            justify-content: center;
            flex: 1;
            min-height: 40px;

            .payment-name {
              font-size: 16px;
              font-weight: 600;
              color: #1f2937;
              line-height: 1.4;
              margin-bottom: 2px;
              transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .payment-desc {
              font-size: 12px;
              color: #9ca3af;
              line-height: 1.3;
              transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            }

            .balance-info {
              font-size: 14px;
              color: #6b7280;
              line-height: 1.3;
              transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);

              &.insufficient {
                color: #ef4444;
                font-weight: 600;
              }
            }
          }

          .payment-check-icon {
            margin-left: 12px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #4285f4;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1);
            flex-shrink: 0;

            i {
              color: white;
              font-size: 14px;
              font-weight: bold;
            }
          }
        }

        &:hover:not(.disabled) .payment-method-content {
          border-color: #4285f4;
          background: #f8f9ff;
          box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
          transform: translateY(-2px);
        }

        &.recommended:hover:not(.disabled):not(.selected) .payment-method-content {
          border-color: #4285f4;
          background: #f8f9ff;
          box-shadow: 0 4px 12px rgba(66, 133, 244, 0.15);
          transform: translateY(-2px);
        }

        /* 推荐支付方式的选中状态 - 优先级更高 */
        &.recommended.selected .payment-method-content {
          border-color: #ff6b35;
          background: linear-gradient(135deg, #fff5f0 0%, #fef7f0 100%);
          box-shadow: 0 4px 16px rgba(255, 107, 53, 0.25);
          transform: translateY(-1px);

          .payment-icon-wrapper {
            background: #fff5f0;
            border: 2px solid #ff6b35;
          }

          .payment-info .payment-name {
            color: #ff6b35;
            font-weight: 700;
          }

          .payment-info .payment-desc {
            color: #e55a2b;
          }

          .payment-check-icon {
            background: #ff6b35;
            opacity: 1;
            transform: scale(1);
          }
        }

        /* 普通支付方式的选中状态 */
        &.selected .payment-method-content {
          border-color: #4285f4;
          background: linear-gradient(135deg, #e8f0fe 0%, #f1f8ff 100%);
          box-shadow: 0 4px 16px rgba(66, 133, 244, 0.25);
          transform: translateY(-1px);

          .payment-icon-wrapper {
            background: #e8f0fe;
            border: 2px solid #4285f4;
          }

          .payment-info .payment-name {
            color: #4285f4;
            font-weight: 700;
          }

          .payment-info .payment-desc {
            color: #1565c0;
          }

          .payment-info .balance-info {
            color: #1565c0;
            font-weight: 600;
          }

          .payment-check-icon {
            opacity: 1;
            transform: scale(1);
          }
        }

        &.disabled {
          cursor: not-allowed;

          .payment-method-content {
            opacity: 0.5;
            background: #f9f9f9;
            border-color: #e0e0e0;

            .payment-icon-wrapper {
              background: #f0f0f0;
            }

            .payment-info .payment-name {
              color: #9e9e9e;
            }

            .payment-info .balance-info {
              color: #bdbdbd;
            }
          }
        }
      }
    }
  }
}

/* 确保所有支付选项宽度统一 */
.purchase-modal {
  .payment-options {
    width: 100%;

    .payment-option {
      width: 100%;

      .el-radio {
        width: 100%;
        display: flex;
        align-items: stretch;
        position: relative;
        margin: 0;

        .el-radio__label {
          width: 100%;
          display: flex;
          flex: 1;
          margin-left: 0;
          padding: 0;

          .payment-method-content {
            width: 100%;
            flex: 1;
            display: flex;
            align-items: center;
            box-sizing: border-box;
          }
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;

  .confirm-pay-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    padding: 12px 24px;
    font-weight: 600;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    i {
      margin-right: 6px;
    }
  }
}

.renewal-dialog {
  text-align: center;

  p {
    margin-bottom: 15px;
    color: var(--text-primary);
  }

  .renewal-options {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
  }
}

// 空状态样式
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 60px 20px;
  text-align: center;

  .empty-icon {
    margin-bottom: 20px;

    i {
      font-size: 4rem;
      color: #dcdfe6;
    }
  }

  .empty-text {
    margin-bottom: 30px;

    h3 {
      margin: 0 0 10px 0;
      font-size: 1.2rem;
      color: var(--text-primary);
    }

    p {
      margin: 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }
}

// 支付方式空状态样式
.empty-payment-methods {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 40px 20px;
  text-align: center;

  .empty-icon {
    margin-bottom: 15px;

    i {
      font-size: 2.5rem;
      color: #dcdfe6;
    }
  }

  .empty-text {
    p {
      margin: 0 0 15px 0;
      color: var(--text-secondary);
      font-size: 0.9rem;
    }
  }
}

</style>
