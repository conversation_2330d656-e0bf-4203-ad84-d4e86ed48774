<template>
  <div class="main-container">


    <!-- 侧边栏 -->
    <div class="sidebar" :class="{
      collapsed: sidebarCollapsed
    }">
      <div class="sidebar-header">
        <div class="header-actions">
          <el-button
            v-if="!sidebarCollapsed"
            type="primary"
            size="medium"
            class="new-chat-btn"
            @click="createNewChat"
          >
            <i class="el-icon-plus"></i>
            新对话
          </el-button>

          <el-button
            v-if="sidebarCollapsed"
            type="primary"
            size="medium"
            class="new-chat-btn-collapsed"
            @click="createNewChat"
            title="新对话"
          >
            <i class="el-icon-plus"></i>
          </el-button>

          <el-button
            type="text"
            class="collapse-btn"
            @click="toggleSidebar"
            :title="sidebarCollapsed ? '展开侧边栏' : '收起侧边栏'"
          >
            <i :class="sidebarCollapsed ? 'el-icon-s-unfold' : 'el-icon-s-fold'"></i>
          </el-button>
        </div>

        <!-- VIP入口按钮 -->
        <div v-if="!sidebarCollapsed" class="vip-entrance">
          <el-button
            v-if="!isVip"
            type="warning"
            size="small"
            class="vip-entrance-btn"
            @click="$router.push('/main/vip-plans')"
          >
            <i class="el-icon-star-on"></i>
            升级VIP
          </el-button>
          <el-button
            v-else
            type="text"
            size="small"
            class="vip-manage-btn"
            @click="$router.push('/main/vip-plans')"
          >
            <i class="el-icon-setting"></i>
            管理会员
          </el-button>
        </div>

        <!-- 收起状态下的VIP入口 -->
        <div v-if="sidebarCollapsed" class="vip-entrance-collapsed">
          <el-button
            type="text"
            size="small"
            class="vip-entrance-btn-collapsed"
            @click="$router.push('/main/vip-plans')"
            :title="isVip ? '管理会员' : '升级VIP'"
          >
            <i class="el-icon-star-on" :class="{ 'vip-active': isVip }"></i>
          </el-button>
        </div>
      </div>
      
      <div class="chat-list">
        <!-- 对话列表头部 -->
        <div
          class="chat-list-header"
          v-if="!sidebarCollapsed"
          @click="toggleChatList"
          :title="chatListCollapsed ? '展开对话列表' : '收起对话列表'"
        >
          <div class="chat-count-info">
            <span v-if="conversations.length === 0" class="chat-count-text">
              没有对话
            </span>
            <span v-else class="chat-count-text">
              对话数量: {{ conversations.length }}
            </span>
          </div>
          <div class="chat-list-toggle-icon">
            <i :class="chatListCollapsed ? 'el-icon-arrow-down' : 'el-icon-arrow-up'"></i>
          </div>
        </div>

        <!-- 对话列表内容 -->
        <div
          class="chat-items-container"
          :class="{ 'collapsed': chatListCollapsed }"
          v-if="!sidebarCollapsed"
        >
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            class="chat-item"
            :class="{
              active: conversation.id === currentConversationId,
              collapsed: sidebarCollapsed
            }"
            @click="selectConversation(conversation.id)"
            :title="sidebarCollapsed ? conversation.title : ''"
          >
            <div class="chat-title" :title="conversation.title">
              {{ conversation.title }}
            </div>
            <div class="chat-actions">
              <el-button
                type="text"
                size="mini"
                @click.stop="renameConversation(conversation)"
              >
                <i class="el-icon-edit"></i>
              </el-button>
              <el-button
                type="text"
                size="mini"
                @click.stop="deleteConversation(conversation.id)"
              >
                <i class="el-icon-delete"></i>
              </el-button>
            </div>
          </div>
        </div>

        <!-- 侧边栏收起时的对话列表 -->
        <div v-if="sidebarCollapsed">
          <div
            v-for="conversation in conversations"
            :key="conversation.id"
            class="chat-item"
            :class="{
              active: conversation.id === currentConversationId,
              collapsed: sidebarCollapsed
            }"
            @click="selectConversation(conversation.id)"
            :title="sidebarCollapsed ? conversation.title : ''"
          >
            <div class="chat-icon">
              <i class="el-icon-chat-dot-round"></i>
            </div>
          </div>
        </div>
      </div>
      
      <div class="sidebar-footer">
        <!-- 演示模式提示 -->
        <div v-if="!sidebarCollapsed" class="demo-mode-tip">
          <i class="el-icon-info"></i>
          <span>演示模式</span>
        </div>

        <!-- VIP状态卡片 -->
        <div v-if="!sidebarCollapsed" class="vip-status-card">
          <div v-if="isVip" class="vip-active-status">
            <div class="vip-badge-large" :class="vipLevel">
              <i class="el-icon-star-on"></i>
              <span>{{ vipLevel === 'super_vip' ? 'SVIP会员' : 'VIP会员' }}</span>
            </div>
            <div class="vip-expire-info">
              <span v-if="vipExpireTime">{{ formatVipExpireTime(vipExpireTime) }}</span>
            </div>
          </div>
          <div v-else class="free-user-status">
            <div class="free-badge">
              <i class="el-icon-user"></i>
              <span>免费用户</span>
            </div>
            <div class="upgrade-prompt">
              <el-button
                type="primary"
                size="mini"
                @click="$router.push('/main/vip-plans')"
                class="upgrade-btn"
              >
                <i class="el-icon-star-on"></i>
                开通会员
              </el-button>
            </div>
          </div>
        </div>

        <div
          class="user-info"
          :class="{ collapsed: sidebarCollapsed }"
          @click="showUserMenu"
          :title="sidebarCollapsed ? `${userInfo.username || '用户'} (${userInfo.email}) - 演示模式` : ''"
        >
          <div class="user-avatar">
            {{ userInfo.username ? userInfo.username.charAt(0).toUpperCase() : 'U' }}
            <!-- 在收起状态下显示VIP标识 -->
            <div v-if="sidebarCollapsed && isVip" class="vip-indicator" :class="vipLevel">
              <i class="el-icon-star-on"></i>
            </div>
          </div>
          <div v-if="!sidebarCollapsed" class="user-details">
            <div class="username-row">
              <div class="username">{{ userInfo.username || '用户' }}</div>
            </div>
            <div class="user-meta">
              <div class="user-email">{{ userInfo.email }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 主内容区 -->
    <div class="main-content">


      <router-view />
    </div>
    
    <!-- 用户菜单 -->
    <el-dropdown
      ref="userDropdown"
      trigger="click"
      placement="top-start"
      @command="handleUserCommand"
    >
      <span style="display: none;"></span>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item command="profile">
          <i class="el-icon-user"></i>
          个人资料
        </el-dropdown-item>
        <el-dropdown-item command="vip">
          <i class="el-icon-star-on"></i>
          VIP会员
          <el-tag v-if="isVip" size="mini" type="warning" class="menu-tag">{{ vipLevel === 'super_vip' ? 'SVIP' : 'VIP' }}</el-tag>
        </el-dropdown-item>
        <el-dropdown-item command="recharge">
          <i class="el-icon-wallet"></i>
          账户充值
          <span class="balance-tag">¥{{ balanceYuan }}</span>
        </el-dropdown-item>
        <el-dropdown-item command="transactions">
          <i class="el-icon-document"></i>
          消费记录
        </el-dropdown-item>
        <el-dropdown-item command="settings">
          <i class="el-icon-setting"></i>
          设置
        </el-dropdown-item>
        <el-dropdown-item command="markdown-test">
          <i class="el-icon-document"></i>
          Markdown测试
        </el-dropdown-item>
        <el-dropdown-item divided command="logout">
          <i class="el-icon-switch-button"></i>
          退出登录
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Main',
  data() {
    return {
      sidebarCollapsed: false,
      chatListCollapsed: false // 对话列表收起状态
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'isVip', 'vipLevel', 'vipExpireTime', 'balanceYuan']),
    ...mapGetters('chat', ['conversations']),
    currentConversationId() {
      return this.$route.params.id
    }
  },
  watch: {
    conversations: {
      handler(newVal, oldVal) {
        console.log('conversations 发生变化:')
        console.log('旧值:', oldVal)
        console.log('新值:', newVal)
      },
      deep: true,
      immediate: true
    },
    sidebarCollapsed: {
      handler(newVal) {
        // 动态调整主内容区的左边距
        this.$nextTick(() => {
          const mainContent = document.querySelector('.main-content')
          if (mainContent) {
            mainContent.style.marginLeft = newVal ? '60px' : '280px'
          }
        })
      },
      immediate: true
    }
  },
  async created() {
    // 加载对话历史
    await this.loadConversations()

    // 如果没有当前对话且有对话列表，选择第一个
    if (!this.currentConversationId && this.conversations.length > 0) {
      this.$router.replace(`/main/chat/${this.conversations[0].id}`)
    }
  },
  mounted() {
    // 确保初始状态下主内容区的左边距正确
    this.$nextTick(() => {
      const mainContent = document.querySelector('.main-content')
      if (mainContent) {
        mainContent.style.marginLeft = this.sidebarCollapsed ? '60px' : '280px'
      }
    })
  },
  methods: {
    ...mapActions('chat', ['createConversation', 'loadConversations']),
    ...mapActions('user', ['logout']),

    formatVipExpireTime(expireTime) {
      if (!expireTime) return ''
      const expire = new Date(expireTime)
      const now = new Date()
      const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))

      if (diffDays <= 0) {
        return '已过期'
      } else if (diffDays <= 7) {
        return `${diffDays}天后到期`
      } else {
        return expire.toLocaleDateString('zh-CN')
      }
    },

    formatVipExpireTime(expireTime) {
      if (!expireTime) return ''
      const expire = new Date(expireTime)
      const now = new Date()
      const diffDays = Math.ceil((expire - now) / (1000 * 60 * 60 * 24))

      if (diffDays <= 0) {
        return '已过期'
      } else if (diffDays <= 7) {
        return `${diffDays}天后到期`
      } else {
        return expire.toLocaleDateString('zh-CN')
      }
    },
    
    toggleSidebar() {
      this.sidebarCollapsed = !this.sidebarCollapsed
    },

    toggleChatList() {
      this.chatListCollapsed = !this.chatListCollapsed
    },


    
    async createNewChat() {
      console.log('createNewChat 被调用')
      console.log('调用前的conversations:', this.conversations)
      console.log('调用前的store状态:', this.$store.state.chat)

      try {
        const conversationId = await this.createConversation()
        console.log('createConversation 返回的ID:', conversationId)
        console.log('调用后的conversations:', this.conversations)
        console.log('调用后的store状态:', this.$store.state.chat)

        // 等待一下确保状态更新
        await this.$nextTick()
        console.log('nextTick后的conversations:', this.conversations)

        this.$router.push(`/main/chat/${conversationId}`)
      } catch (error) {
        console.error('创建新对话失败:', error)
        this.$message.error('创建新对话失败，请重试')
      }
    },
    
    selectConversation(id) {
      this.$router.push(`/main/chat/${id}`)
    },
    
    async deleteConversation(id) {
      try {
        await this.$confirm('确定要删除这个对话吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        this.$store.dispatch('chat/deleteConversation', id)
        
        // 如果删除的是当前对话，跳转到第一个对话或创建新对话
        if (id === this.currentConversationId) {
          if (this.conversations.length > 0) {
            this.$router.replace(`/main/chat/${this.conversations[0].id}`)
          } else {
            const newId = await this.createConversation()
            this.$router.replace(`/main/chat/${newId}`)
          }
        }
      } catch (error) {
        // 用户取消删除
      }
    },
    
    async renameConversation(conversation) {
      try {
        const { value } = await this.$prompt('请输入新的对话标题', '重命名对话', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: conversation.title,
          inputValidator: (value) => {
            if (!value || !value.trim()) {
              return '标题不能为空'
            }
            return true
          }
        })
        
        this.$store.dispatch('chat/renameConversation', { id: conversation.id, title: value.trim() })
      } catch (error) {
        // 用户取消重命名
      }
    },
    
    showUserMenu() {
      this.$refs.userDropdown.show()
    },
    
    handleUserCommand(command) {
      switch (command) {
        case 'profile':
          this.$router.push('/main/profile')
          break
        case 'vip':
          this.$router.push('/main/vip-plans')
          break
        case 'recharge':
          this.$router.push('/main/recharge')
          break
        case 'transactions':
          this.$router.push('/main/transactions')
          break
        case 'settings':
          this.$router.push('/main/settings')
          break
        case 'markdown-test':
          this.$router.push('/main/markdown-test-new')
          break
        case 'logout':
          this.handleLogout()
          break
      }
    },
    
    async handleLogout() {
      try {
        await this.$confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        
        this.logout()
        this.$router.push('/login')
      } catch (error) {
        // 用户取消退出
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.main-container {
  height: 100vh;
  display: flex;
  background: var(--bg-secondary);
  overflow: hidden; /* 防止整个容器滚动 */
}

.sidebar {
  width: 280px;
  background: var(--bg-tertiary);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  position: fixed; /* 固定定位 */
  left: 0;
  top: 0;
  height: 100vh; /* 占满整个视口高度 */
  z-index: 1000; /* 确保在其他内容之上 */

  &.collapsed {
    width: 60px;
  }
}

.sidebar-header {
  padding: 16px;
  border-bottom: 1px solid var(--border-color);

  .collapsed & {
    display: flex;
    flex-direction: column;
    gap: 12px;
  }
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;

  .collapsed & {
    flex-direction: column;
    margin-bottom: 8px;
  }
}

// VIP入口样式
.vip-entrance {
  display: flex;
  justify-content: center;
}

.vip-entrance-btn {
  width: 100%;
  height: 32px;
  font-size: 12px;
  font-weight: 600;
  border-radius: 16px;

  i {
    margin-right: 4px;
    font-size: 12px;
  }
}

.vip-manage-btn {
  width: 100%;
  height: 32px;
  font-size: 12px;
  color: var(--text-secondary);

  &:hover {
    color: var(--primary-color);
  }

  i {
    margin-right: 4px;
    font-size: 12px;
  }
}

.vip-entrance-collapsed {
  display: flex;
  justify-content: center;
}

.vip-entrance-btn-collapsed {
  width: 36px;
  height: 36px;
  padding: 0;
  border-radius: 50%;

  i {
    font-size: 16px;
    color: var(--text-secondary);
    transition: color 0.2s ease;

    &.vip-active {
      color: #f39c12;
    }
  }

  &:hover i {
    color: var(--primary-color);
  }
}

.new-chat-btn {
  flex: 1;
  height: 36px;
}

.new-chat-btn-collapsed {
  width: 36px;
  height: 36px;
  padding: 0;
}

.collapse-btn {
  width: 36px;
  height: 36px;
  padding: 0;

  .collapsed & {
    order: -1;
  }
}

.chat-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

// 对话列表头部样式
.chat-list-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 8px;
  border-bottom: 1px solid var(--border-color);
  margin-bottom: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--bg-secondary);

    .chat-list-toggle-icon i {
      color: var(--primary-color);
    }
  }
}

.chat-count-info {
  flex: 1;
  pointer-events: none; // 防止文字选择
}

.chat-count-text {
  color: var(--text-secondary);
  font-size: 12px;
  user-select: none; // 防止文字选择
}

.chat-list-toggle-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  color: var(--text-secondary);
  transition: color 0.2s ease;

  i {
    font-size: 14px;
  }
}

// 对话列表容器样式
.chat-items-container {
  transition: all 0.3s ease;
  overflow: hidden;

  &.collapsed {
    max-height: 0;
    opacity: 0;
  }
}

.chat-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  margin-bottom: 4px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--bg-secondary);
  }

  &.active {
    background: var(--primary-color);
    color: white;
  }

  &.collapsed {
    justify-content: center;
    padding: 8px;
  }
}

.chat-icon {
  font-size: 18px;
  color: var(--text-secondary);

  .chat-item.active & {
    color: white;
  }
}

.chat-title {
  flex: 1;
  font-size: var(--font-sm);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.chat-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  
  .chat-item:hover & {
    opacity: 1;
  }
  
  .el-button {
    padding: 4px;
    min-height: auto;
  }
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid var(--border-color);
}

// VIP状态卡片样式
.vip-status-card {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
}

.vip-active-status {
  text-align: center;
}

.vip-badge-large {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  margin-bottom: 6px;

  &.vip {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  }

  &.super_vip {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  }

  i {
    font-size: 12px;
  }
}

.vip-expire-info {
  font-size: 11px;
  color: var(--text-secondary);
}

.free-user-status {
  text-align: center;
}

.free-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  margin-bottom: 8px;

  i {
    font-size: 12px;
  }
}

.upgrade-prompt {
  .upgrade-btn {
    width: 100%;
    font-size: 11px;
    padding: 6px 12px;
    height: auto;
    border-radius: 12px;

    i {
      margin-right: 4px;
    }
  }
}

.demo-mode-tip {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 8px;
  margin-bottom: 12px;
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 4px;
  font-size: var(--font-xs);
  color: var(--warning-color);

  i {
    font-size: 12px;
  }
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 6px;
  transition: background-color 0.2s ease;

  &:hover {
    background: var(--bg-secondary);
  }

  &.collapsed {
    justify-content: center;
    gap: 0;
  }
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: var(--font-sm);
  position: relative;
}

.vip-indicator {
  position: absolute;
  top: -2px;
  right: -2px;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2px solid white;
  font-size: 8px;

  &.vip {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  }

  &.super_vip {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  }

  i {
    color: white;
  }
}

.user-details {
  flex: 1;
  min-width: 0;
}

.username-row {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 2px;
}

.username {
  font-size: var(--font-sm);
  font-weight: 500;
  color: var(--text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  flex: 1;
}

.vip-badge {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 10px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 2px;

  &.vip {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  }

  &.super_vip {
    background: linear-gradient(135deg, #9b59b6 0%, #8e44ad 100%);
  }

  i {
    font-size: 10px;
  }
}

.user-meta {
  display: flex;
  flex-direction: column;
  gap: 1px;
}

.user-email {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.vip-expire {
  font-size: 10px;
  color: var(--text-tertiary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
  margin-left: 280px; /* 为固定的侧边栏留出空间 */
  height: 100vh; /* 占满整个视口高度 */
  overflow-y: auto; /* 允许主内容区垂直滚动 */
  transition: margin-left 0.3s ease; /* 平滑过渡效果 */
}



// 用户菜单样式
.menu-tag {
  margin-left: 8px;
}

.balance-tag {
  margin-left: 8px;
  font-size: 12px;
  color: var(--text-secondary);
  background: var(--bg-secondary);
  padding: 2px 6px;
  border-radius: 10px;
}


</style>
