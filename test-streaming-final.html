<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>最终流式 Markdown 渲染测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .control-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .control-button.primary {
            background: #409eff;
            color: white;
        }

        .control-button.primary:hover {
            background: #337ecc;
        }

        .control-button.success {
            background: #67c23a;
            color: white;
        }

        .control-button.success:hover {
            background: #5daf34;
        }

        .control-button.danger {
            background: #f56c6c;
            color: white;
        }

        .control-button.danger:hover {
            background: #f45656;
        }

        .control-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status-info {
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-info h3 {
            color: #409eff;
            margin-bottom: 10px;
        }

        .status-stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .stat-item {
            margin: 5px;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .stat-value {
            font-weight: bold;
            color: #409eff;
        }

        .rendered-output {
            min-height: 400px;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #409eff;
            transition: width 0.3s ease;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
        }

        .rendered-output a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>最终流式 Markdown 渲染测试</h1>
                <p>验证专门优化的流式渲染器效果</p>
            </div>

            <div class="test-container">
                <div class="control-section">
                    <div class="section-title">控制面板</div>
                    
                    <div class="control-buttons">
                        <button class="control-button primary" @click="startStreaming" :disabled="isStreaming">
                            开始流式输出
                        </button>
                        <button class="control-button danger" @click="stopStreaming" :disabled="!isStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status-info">
                        <h3>状态信息</h3>
                        <div class="status-stats">
                            <div class="stat-item">
                                <div class="stat-label">流式状态</div>
                                <div class="stat-value">{{ streamStatus }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">进度</div>
                                <div class="stat-value">{{ progress }}%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">字符数</div>
                                <div class="stat-value">{{ currentContent.length }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">更新次数</div>
                                <div class="stat-value">{{ updateCount }}</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button class="control-button primary" @click="loadExample('complex')">复杂示例</button>
                        <button class="control-button primary" @click="loadExample('list')">列表示例</button>
                        <button class="control-button success" @click="testSpeed('fast')">快速测试</button>
                        <button class="control-button success" @click="testSpeed('slow')">慢速测试</button>
                    </div>
                </div>

                <div class="output-section">
                    <div class="section-title">
                        渲染结果
                        <span v-if="isStreaming" class="streaming-indicator">●</span>
                    </div>
                    <div class="rendered-output" v-html="renderedHtml"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                currentContent: '',
                renderedHtml: '',
                md: null,
                isStreaming: false,
                streamTimer: null,
                currentExample: '',
                currentIndex: 0,
                updateCount: 0,
                streamSpeed: 50, // 默认速度
                examples: {
                    complex: `# 人工智能技术深度解析

## 🤖 什么是人工智能？

人工智能（**Artificial Intelligence**，简称 *AI*）是指由人创造的能够感知环境、学习知识、逻辑推理和执行任务的科学技术。

### 核心技术领域

#### 1. 机器学习 (Machine Learning)
- **监督学习**：使用标记数据训练模型
  - 分类算法
  - 回归算法
- **无监督学习**：从未标记数据中发现模式
  - 聚类算法
  - 降维技术
- **强化学习**：通过奖励机制学习最优策略

#### 2. 深度学习 (Deep Learning)

深度学习是机器学习的一个子集，使用多层神经网络：

\`\`\`python
import tensorflow as tf
import numpy as np

# 创建简单的神经网络
model = tf.keras.Sequential([
    tf.keras.layers.Dense(128, activation='relu', input_shape=(784,)),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Dense(10, activation='softmax')
])

# 编译模型
model.compile(
    optimizer='adam',
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy']
)

print("模型创建成功！")
\`\`\`

#### 3. 自然语言处理 (NLP)

> "语言是思维的工具，理解语言就是理解智能的关键。"

NLP 的主要任务包括：
- 文本分析和理解
- 机器翻译
- 对话系统
- 情感分析

### 应用场景对比

| 领域 | 应用 | 成熟度 | 市场规模 |
|------|------|--------|----------|
| 医疗健康 | 诊断辅助、药物发现 | ⭐⭐⭐⭐ | $45B |
| 自动驾驶 | L4/L5 自动驾驶 | ⭐⭐⭐ | $31B |
| 金融科技 | 风险评估、算法交易 | ⭐⭐⭐⭐⭐ | $22B |
| 智能制造 | 质量检测、预测维护 | ⭐⭐⭐⭐ | $16B |

---

## 🚀 未来发展趋势

1. **通用人工智能 (AGI)**
2. **边缘计算 + AI**
3. **可解释性 AI**
4. **AI 伦理与安全**

**总结**：AI 技术正在快速发展，将深刻改变我们的生活和工作方式。`,
                    list: `# 📋 任务清单管理系统

## 今日待办事项

### 🔥 高优先级
- [x] 完成流式 Markdown 渲染器优化
- [x] 修复缓存机制问题
- [ ] 编写单元测试
- [ ] 更新文档

### 📝 开发任务
1. **前端优化**
   - [x] 组件重构
   - [ ] 性能测试
   - [ ] UI/UX 改进

2. **后端开发**
   - [x] API 接口设计
   - [x] 数据库优化
   - [ ] 安全性加固

3. **测试与部署**
   - [ ] 集成测试
   - [ ] 压力测试
   - [ ] 生产环境部署

### 💡 技术研究
- [ ] 研究新的 AI 模型
- [ ] 学习 Vue 3 Composition API
- [ ] 探索微前端架构

## 本周目标

> **重要提醒**：确保所有功能都经过充分测试！

\`\`\`bash
# 运行测试命令
npm run test
npm run e2e
npm run lint
\`\`\`

---

**进度统计**：已完成 6/12 项任务 (50%)`
                }
            },
            computed: {
                streamStatus() {
                    return this.isStreaming ? '流式输出中' : '未开始'
                },
                progress() {
                    if (!this.currentExample) return 0
                    return Math.round((this.currentIndex / this.currentExample.length) * 100)
                }
            },
            mounted() {
                this.initMarkdown()
                this.loadExample('complex')
            },
            methods: {
                // 初始化 Markdown-it
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        console.log('✅ Markdown-it 初始化成功')
                    } else {
                        console.error('❌ markdown-it 库未加载')
                    }
                },
                
                // 更新输出
                updateOutput() {
                    if (!this.md) {
                        this.renderedHtml = '<p style="color: red;">Markdown-it 库未加载</p>'
                        return
                    }
                    
                    if (!this.currentContent) {
                        this.renderedHtml = ''
                        return
                    }
                    
                    this.updateCount++
                    
                    try {
                        // 直接渲染 - 确保流式输出时正确解析
                        this.renderedHtml = this.md.render(this.currentContent)
                    } catch (error) {
                        console.error('Markdown渲染失败:', error)
                        this.renderedHtml = `<p style="color: red;">渲染错误: ${error.message}</p>`
                    }
                },
                
                startStreaming() {
                    if (!this.currentExample) {
                        this.loadExample('complex')
                    }
                    
                    this.isStreaming = true
                    this.currentIndex = 0
                    this.currentContent = ''
                    this.updateCount = 0
                    
                    this.streamTimer = setInterval(() => {
                        if (this.currentIndex < this.currentExample.length) {
                            // 模拟流式输出，每次添加1-5个字符
                            const chunkSize = Math.floor(Math.random() * 5) + 1
                            const endIndex = Math.min(this.currentIndex + chunkSize, this.currentExample.length)
                            
                            this.currentContent = this.currentExample.substring(0, endIndex)
                            this.currentIndex = endIndex
                            
                            this.updateOutput()
                            
                            if (this.currentIndex >= this.currentExample.length) {
                                this.stopStreaming()
                            }
                        }
                    }, this.streamSpeed)
                },
                
                stopStreaming() {
                    this.isStreaming = false
                    if (this.streamTimer) {
                        clearInterval(this.streamTimer)
                        this.streamTimer = null
                    }
                    
                    // 流式完成后，确保最终渲染是正确的
                    this.$nextTick(() => {
                        this.updateOutput()
                    })
                },
                
                loadExample(type) {
                    this.stopStreaming()
                    this.currentExample = this.examples[type] || this.examples.complex
                    this.currentContent = this.currentExample
                    this.currentIndex = this.currentExample.length
                    this.updateOutput()
                },
                
                testSpeed(speed) {
                    this.stopStreaming()
                    this.streamSpeed = speed === 'fast' ? 20 : 100
                    this.startStreaming()
                }
            },
            
            beforeDestroy() {
                this.stopStreaming()
            }
        })
    </script>
</body>
</html>
