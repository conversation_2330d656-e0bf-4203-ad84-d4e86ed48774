<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>直接 DOM 操作解决流式 Markdown 延迟</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .test-section {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #409eff;
            color: white;
        }

        .btn-primary:hover {
            background: #337ecc;
        }

        .btn-success {
            background: #67c23a;
            color: white;
        }

        .btn-success:hover {
            background: #5daf34;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #f45656;
        }

        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status {
            padding: 12px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .message-container {
            min-height: 300px;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            padding: 15px;
        }

        .message {
            margin-bottom: 15px;
            padding: 12px;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .message.ai {
            background: #e8f4fd;
            border-left: 4px solid #409eff;
        }

        .message.user {
            background: #f0f9ff;
            border-left: 4px solid #67c23a;
        }

        .message-header {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }

        .message-content {
            font-size: 14px;
            line-height: 1.6;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Markdown 样式 */
        .message-content h1, .message-content h2, .message-content h3 {
            color: #333;
            margin: 12px 0 8px 0;
            font-weight: 600;
        }

        .message-content h1 {
            font-size: 1.4em;
            border-bottom: 2px solid #eee;
            padding-bottom: 4px;
        }

        .message-content h2 {
            font-size: 1.2em;
            border-bottom: 1px solid #eee;
            padding-bottom: 3px;
        }

        .message-content h3 {
            font-size: 1.1em;
            color: #409eff;
        }

        .message-content p {
            margin: 8px 0;
        }

        .message-content ul, .message-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .message-content li {
            margin: 3px 0;
        }

        .message-content strong {
            font-weight: 700;
            color: #409eff;
        }

        .message-content em {
            font-style: italic;
            color: #666;
        }

        .message-content code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .message-content pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin: 10px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .message-content blockquote {
            margin: 10px 0;
            padding: 8px 12px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 6px 6px 0;
            font-style: italic;
            color: #666;
        }

        .comparison {
            background: #fff9e6;
            border: 1px solid #ffd700;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .comparison h3 {
            color: #e6a23c;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🚀 直接 DOM 操作解决流式 Markdown 延迟</h1>
                <p>绕过 Vue 响应式系统，直接操作 DOM 实现实时 Markdown 渲染</p>
            </div>

            <div class="test-section">
                <div class="section-title">💬 模拟聊天界面</div>
                
                <div class="controls">
                    <button class="btn btn-primary" @click="startStreamingTest" :disabled="isStreaming">
                        开始流式测试
                    </button>
                    <button class="btn btn-danger" @click="stopStreaming" :disabled="!isStreaming">
                        停止流式
                    </button>
                    <button class="btn btn-success" @click="clearMessages">
                        清空消息
                    </button>
                </div>

                <div class="status">
                    状态: {{ status }}
                    <span v-if="isStreaming" class="streaming-indicator">●</span>
                    <br>
                    消息数: {{ messages.length }} | 当前字符: {{ currentMessage?.content?.length || 0 }} | 渲染次数: {{ renderCount }}
                </div>

                <div class="message-container" ref="messageContainer">
                    <div 
                        v-for="message in messages" 
                        :key="message.id"
                        class="message"
                        :class="message.type"
                    >
                        <div class="message-header">
                            {{ message.type === 'user' ? '👤 用户' : '🤖 AI' }} - {{ formatTime(message.timestamp) }}
                            <span v-if="message.isStreaming" class="streaming-indicator">流式中</span>
                        </div>
                        <div 
                            class="message-content"
                            :ref="'message-' + message.id"
                            v-html="getMessageContent(message)"
                        ></div>
                    </div>
                </div>
            </div>

            <div class="comparison">
                <h3>🔍 技术原理</h3>
                <p><strong>核心思路</strong>：流式状态下使用直接 DOM 操作，非流式状态使用 Vue 响应式</p>
                <p><strong>关键技术</strong>：ref 引用 + innerHTML 直接更新 + 绕过 computed 缓存</p>
                <p><strong>预期效果</strong>：流式 Markdown 渲染无延迟，实时更新</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                // Markdown 渲染器
                md: null,
                
                // 消息列表
                messages: [],
                
                // 流式状态
                isStreaming: false,
                streamTimer: null,
                currentMessage: null,
                
                // 测试内容
                testContent: `# 🚀 AI 助手回复

## 关于 Vue.js 的特点

Vue.js 是一个**渐进式框架**，具有以下显著特点：

### ⭐ 1. 渐进式框架
- 无需一开始就全盘使用：你可以逐步引入 Vue.js，从简单的 DOM 操作到完整的单页应用 (SPA)。
- 可以与现有项目集成，也可以作为独立框架使用。

### 🧩 2. 组件化开发
- 应用被拆分为多个可复用的组件，每个组件包含自己的模板、逻辑和样式。
- 提高代码复用性、可维护性和开发效率。

### 📊 3. 响应式数据绑定
- 数据变化自动更新视图，视图变化也可以更新数据（双向绑定）。
- 使用 \`Object.defineProperty\` 或 \`Proxy\` 实现响应式系统（Vue 2vs Vue 3）。

### 🎯 4. 虚拟 DOM
- 使用虚拟 DOM 提高渲染性能
- 通过 diff 算法最小化实际 DOM 操作

### 💻 代码示例

\`\`\`javascript
// Vue 组件示例
new Vue({
  el: '#app',
  data: {
    message: 'Hello Vue!'
  },
  methods: {
    updateMessage() {
      this.message = 'Vue is awesome!'
    }
  }
})
\`\`\`

> **总结**: Vue.js 以其简洁的 API、灵活的架构和强大的生态系统，成为现代前端开发的热门选择。

---

**希望这个回答对您有帮助！** 🎉`,
                
                // 状态
                renderCount: 0,
                currentIndex: 0
            },
            computed: {
                status() {
                    return this.isStreaming ? '流式输出中' : '待机中'
                }
            },
            mounted() {
                this.initMarkdown()
            },
            beforeDestroy() {
                this.cleanup()
            },
            methods: {
                // 初始化 Markdown
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        console.log('✅ Markdown-it 初始化成功')
                    }
                },

                // 获取消息内容（关键方法）
                getMessageContent(message) {
                    if (!this.md || !message.content) {
                        return message.content || ''
                    }

                    // 流式消息：直接 DOM 操作，不依赖 Vue 响应式
                    if (message.isStreaming) {
                        this.$nextTick(() => {
                            this.directUpdateDOM(message)
                        })
                        return '' // 返回空，让直接 DOM 操作处理
                    }

                    // 非流式消息：正常 Markdown 渲染
                    try {
                        return this.md.render(message.content)
                    } catch (error) {
                        console.error('Markdown 渲染失败:', error)
                        return message.content
                    }
                },

                // 直接更新 DOM（核心方法）
                directUpdateDOM(message) {
                    const element = this.$refs['message-' + message.id]
                    if (element && element[0] && this.md) {
                        try {
                            const rendered = this.md.render(message.content)
                            element[0].innerHTML = rendered
                            this.renderCount++
                            
                            console.log(`⚡ 直接DOM更新: 消息${message.id}, ${message.content.length}字符, 第${this.renderCount}次渲染`)
                        } catch (error) {
                            console.error('直接DOM渲染失败:', error)
                            element[0].innerHTML = message.content
                        }
                    }
                },

                // 开始流式测试
                startStreamingTest() {
                    // 添加用户消息
                    const userMessage = {
                        id: 'user_' + Date.now(),
                        type: 'user',
                        content: '请介绍一下 Vue.js 的特点',
                        timestamp: new Date(),
                        isStreaming: false
                    }
                    this.messages.push(userMessage)

                    // 添加 AI 消息（开始流式）
                    const aiMessage = {
                        id: 'ai_' + Date.now(),
                        type: 'ai',
                        content: '',
                        timestamp: new Date(),
                        isStreaming: true
                    }
                    this.messages.push(aiMessage)
                    this.currentMessage = aiMessage

                    // 开始流式输出
                    this.isStreaming = true
                    this.currentIndex = 0
                    this.renderCount = 0

                    this.streamTimer = setInterval(() => {
                        if (this.currentIndex < this.testContent.length) {
                            // 模拟流式数据接收
                            const chunkSize = Math.floor(Math.random() * 5) + 1
                            const endIndex = Math.min(this.currentIndex + chunkSize, this.testContent.length)
                            
                            this.currentMessage.content = this.testContent.substring(0, endIndex)
                            this.currentIndex = endIndex

                            // 直接更新 DOM
                            this.directUpdateDOM(this.currentMessage)

                            // 滚动到底部
                            this.$nextTick(() => {
                                this.scrollToBottom()
                            })

                            if (this.currentIndex >= this.testContent.length) {
                                this.stopStreaming()
                            }
                        }
                    }, 50) // 50ms 间隔，模拟高频数据
                },

                // 停止流式
                stopStreaming() {
                    this.isStreaming = false
                    if (this.streamTimer) {
                        clearInterval(this.streamTimer)
                        this.streamTimer = null
                    }
                    if (this.currentMessage) {
                        this.currentMessage.isStreaming = false
                        // 最终渲染
                        this.$forceUpdate()
                    }
                },

                // 清空消息
                clearMessages() {
                    this.messages = []
                    this.renderCount = 0
                },

                // 滚动到底部
                scrollToBottom() {
                    const container = this.$refs.messageContainer
                    if (container) {
                        container.scrollTop = container.scrollHeight
                    }
                },

                // 格式化时间
                formatTime(date) {
                    return date.toLocaleTimeString()
                },

                // 清理资源
                cleanup() {
                    this.stopStreaming()
                }
            }
        })
    </script>
</body>
</html>
