<template>
  <div class="markdown-renderer" v-html="renderedContent"></div>
</template>

<script>
import MarkdownIt from 'markdown-it'
import markdownItAnchor from 'markdown-it-anchor'
import markdownItHighlightjs from 'markdown-it-highlightjs'
import markdownItTaskLists from 'markdown-it-task-lists'
import markdownItToc from 'markdown-it-table-of-contents'
import hljs from 'highlight.js/lib/core'

// 导入常用的语言支持
import javascript from 'highlight.js/lib/languages/javascript'
import typescript from 'highlight.js/lib/languages/typescript'
import java from 'highlight.js/lib/languages/java'
import python from 'highlight.js/lib/languages/python'
import css from 'highlight.js/lib/languages/css'
import html from 'highlight.js/lib/languages/xml'
import sql from 'highlight.js/lib/languages/sql'
import json from 'highlight.js/lib/languages/json'
import yaml from 'highlight.js/lib/languages/yaml'
import bash from 'highlight.js/lib/languages/bash'
import cpp from 'highlight.js/lib/languages/cpp'
import csharp from 'highlight.js/lib/languages/csharp'
import go from 'highlight.js/lib/languages/go'
import rust from 'highlight.js/lib/languages/rust'
import php from 'highlight.js/lib/languages/php'
import ruby from 'highlight.js/lib/languages/ruby'
import kotlin from 'highlight.js/lib/languages/kotlin'
import swift from 'highlight.js/lib/languages/swift'
import markdown from 'highlight.js/lib/languages/markdown'

// 注册语言
hljs.registerLanguage('javascript', javascript)
hljs.registerLanguage('js', javascript)
hljs.registerLanguage('typescript', typescript)
hljs.registerLanguage('ts', typescript)
hljs.registerLanguage('java', java)
hljs.registerLanguage('python', python)
hljs.registerLanguage('py', python)
hljs.registerLanguage('css', css)
hljs.registerLanguage('html', html)
hljs.registerLanguage('xml', html)
hljs.registerLanguage('sql', sql)
hljs.registerLanguage('json', json)
hljs.registerLanguage('yaml', yaml)
hljs.registerLanguage('yml', yaml)
hljs.registerLanguage('bash', bash)
hljs.registerLanguage('sh', bash)
hljs.registerLanguage('shell', bash)
hljs.registerLanguage('cpp', cpp)
hljs.registerLanguage('c++', cpp)
hljs.registerLanguage('c', cpp)
hljs.registerLanguage('csharp', csharp)
hljs.registerLanguage('cs', csharp)
hljs.registerLanguage('go', go)
hljs.registerLanguage('rust', rust)
hljs.registerLanguage('rs', rust)
hljs.registerLanguage('php', php)
hljs.registerLanguage('ruby', ruby)
hljs.registerLanguage('rb', ruby)
hljs.registerLanguage('kotlin', kotlin)
hljs.registerLanguage('kt', kotlin)
hljs.registerLanguage('swift', swift)
hljs.registerLanguage('markdown', markdown)
hljs.registerLanguage('md', markdown)

export default {
  name: 'MarkdownRenderer',
  props: {
    // Markdown内容
    content: {
      type: String,
      default: ''
    },
    // 是否启用代码高亮
    enableHighlight: {
      type: Boolean,
      default: true
    },
    // 是否启用目录
    enableToc: {
      type: Boolean,
      default: false
    },
    // 是否启用锚点
    enableAnchor: {
      type: Boolean,
      default: true
    },
    // 是否启用任务列表
    enableTaskLists: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      md: null
    }
  },
  created() {
    this.initMarkdownIt()
  },
  watch: {
    // 监听配置变化，重新初始化
    enableHighlight() {
      this.initMarkdownIt()
    },
    enableAnchor() {
      this.initMarkdownIt()
    },
    enableTaskLists() {
      this.initMarkdownIt()
    },
    enableToc() {
      this.initMarkdownIt()
    }
  },
  computed: {
    renderedContent() {
      if (!this.content || !this.md) return ''

      try {
        let html = this.md.render(this.content)

        // 后处理：添加自定义CSS类
        html = this.addCustomClasses(html)

        return html
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        // 提供更友好的错误处理
        return `<div class="markdown-error">
          <p style="color: #f56c6c; margin: 10px 0;">
            <i class="el-icon-warning"></i> Markdown 渲染失败
          </p>
          <details style="margin-top: 10px;">
            <summary style="cursor: pointer; color: #909399;">查看详细错误信息</summary>
            <pre style="background: #f5f5f5; padding: 10px; margin-top: 5px; border-radius: 4px; font-size: 12px; color: #666;">${error.message}</pre>
          </details>
          <div style="margin-top: 10px; padding: 10px; background: #fdf6ec; border-left: 4px solid #e6a23c; border-radius: 4px;">
            <p style="margin: 0; color: #e6a23c; font-size: 14px;">原始内容：</p>
            <pre style="margin: 5px 0 0 0; white-space: pre-wrap; font-size: 13px; color: #666;">${this.escapeHtml(this.content)}</pre>
          </div>
        </div>`
      }
    }
  },
  methods: {
    // 初始化 markdown-it
    initMarkdownIt() {
      try {
        // 创建 markdown-it 实例
        this.md = new MarkdownIt({
          html: true,        // 启用HTML标签
          xhtmlOut: false,   // 使用 '>' 来关闭单标签
          breaks: true,      // 转换段落里的 '\n' 到 <br>
          langPrefix: 'language-',  // 给围栏代码块的CSS语言前缀
          linkify: true,     // 将类似URL的文本自动转换为链接
          typographer: true, // 启用一些语言中性的替换 + 引号美化
          quotes: '""\'\'',    // 双引号和单引号的替换对
        })

        // 配置代码高亮
        if (this.enableHighlight) {
          try {
            this.md.use(markdownItHighlightjs, {
              hljs: hljs,
              auto: true,
              code: true
            })
          } catch (error) {
            console.warn('代码高亮插件加载失败:', error)
          }
        }

        // 配置锚点 - 使用简单配置避免样式冲突
        if (this.enableAnchor) {
          try {
            this.md.use(markdownItAnchor, {
              permalink: false,  // 禁用永久链接，只生成 id
              permalinkBefore: false,
              permalinkSymbol: '',
              level: [1, 2, 3, 4, 5, 6]
            })
          } catch (error) {
            console.warn('锚点插件加载失败:', error)
          }
        }
      } catch (error) {
        console.error('Markdown-it 初始化失败:', error)
        // 创建一个最基础的实例作为后备
        this.md = new MarkdownIt({
          html: true,
          breaks: true,
          linkify: true
        })
      }

        // 配置任务列表
        if (this.enableTaskLists) {
          try {
            this.md.use(markdownItTaskLists, {
              enabled: true,
              label: true,
              labelAfter: true
            })
          } catch (error) {
            console.warn('任务列表插件加载失败:', error)
          }
        }

        // 配置目录
        if (this.enableToc) {
          try {
            this.md.use(markdownItToc, {
              includeLevel: [1, 2, 3, 4, 5, 6],
              containerClass: 'markdown-toc',
              markerPattern: /^\[\[toc\]\]/im,
              listType: 'ul',
              format: function(anchor, htmlContent) {
                return htmlContent
              }
            })
          } catch (error) {
            console.warn('目录插件加载失败:', error)
          }
        }

        // 自定义渲染规则
        this.customizeRenderer()

        // 注册语言支持
        this.registerLanguages()
    },

    // 自定义渲染器
    customizeRenderer() {
      // 自定义段落渲染
      const defaultParagraphRender = this.md.renderer.rules.paragraph_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.paragraph_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-paragraph')
        return defaultParagraphRender(tokens, idx, options, env, renderer)
      }

      // 自定义标题渲染
      const defaultHeadingRender = this.md.renderer.rules.heading_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.heading_open = function(tokens, idx, options, env, renderer) {
        const level = tokens[idx].tag.slice(1)
        tokens[idx].attrJoin('class', `markdown-heading markdown-h${level}`)
        return defaultHeadingRender(tokens, idx, options, env, renderer)
      }

      // 自定义列表渲染
      const defaultListRender = this.md.renderer.rules.bullet_list_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.bullet_list_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-list')
        return defaultListRender(tokens, idx, options, env, renderer)
      }

      const defaultOrderedListRender = this.md.renderer.rules.ordered_list_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.ordered_list_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-list')
        return defaultOrderedListRender(tokens, idx, options, env, renderer)
      }

      // 自定义列表项渲染
      const defaultListItemRender = this.md.renderer.rules.list_item_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.list_item_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-list-item')
        return defaultListItemRender(tokens, idx, options, env, renderer)
      }

      // 自定义引用块渲染
      const defaultBlockquoteRender = this.md.renderer.rules.blockquote_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.blockquote_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-blockquote')
        return defaultBlockquoteRender(tokens, idx, options, env, renderer)
      }

      // 自定义表格渲染
      const defaultTableRender = this.md.renderer.rules.table_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.table_open = function(tokens, idx, options, env, renderer) {
        return '<div class="markdown-table-wrapper"><table class="markdown-table">'
      }

      const defaultTableCloseRender = this.md.renderer.rules.table_close || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.table_close = function(tokens, idx, options, env, renderer) {
        return '</table></div>'
      }

      // 自定义表格行渲染
      const defaultTrRender = this.md.renderer.rules.tr_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.tr_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-table-row')
        return defaultTrRender(tokens, idx, options, env, renderer)
      }

      // 自定义表格单元格渲染
      const defaultTdRender = this.md.renderer.rules.td_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.td_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-table-cell')
        return defaultTdRender(tokens, idx, options, env, renderer)
      }

      const defaultThRender = this.md.renderer.rules.th_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.th_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-table-cell')
        return defaultThRender(tokens, idx, options, env, renderer)
      }

      // 自定义分隔线渲染
      this.md.renderer.rules.hr = function(tokens, idx, options, env, renderer) {
        return '<hr class="markdown-hr" />\n'
      }

      // 自定义链接渲染
      const defaultLinkRender = this.md.renderer.rules.link_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.link_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-link')
        tokens[idx].attrSet('target', '_blank')
        tokens[idx].attrSet('rel', 'noopener noreferrer')
        return defaultLinkRender(tokens, idx, options, env, renderer)
      }

      // 自定义图片渲染
      this.md.renderer.rules.image = function(tokens, idx, options, env, renderer) {
        const token = tokens[idx]
        token.attrJoin('class', 'markdown-image')
        token.attrSet('loading', 'lazy')
        return renderer.renderToken(tokens, idx, options)
      }

      // 自定义行内代码渲染
      this.md.renderer.rules.code_inline = function(tokens, idx, options, env, renderer) {
        const token = tokens[idx]
        const attrs = token.attrJoin('class', 'inline-code')
        return `<code${attrs}>${renderer.utils.escapeHtml(token.content)}</code>`
      }

      // 自定义强调渲染
      const defaultStrongRender = this.md.renderer.rules.strong_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.strong_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-strong')
        return defaultStrongRender(tokens, idx, options, env, renderer)
      }

      // 自定义斜体渲染
      const defaultEmRender = this.md.renderer.rules.em_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.em_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-em')
        return defaultEmRender(tokens, idx, options, env, renderer)
      }

      // 自定义删除线渲染
      const defaultDelRender = this.md.renderer.rules.s_open || function(tokens, idx, options, env, renderer) {
        return renderer.renderToken(tokens, idx, options)
      }
      this.md.renderer.rules.s_open = function(tokens, idx, options, env, renderer) {
        tokens[idx].attrJoin('class', 'markdown-del')
        return defaultDelRender(tokens, idx, options, env, renderer)
      }
    },

    // 添加自定义CSS类（后处理）
    addCustomClasses(html) {
      // 为任务列表添加自定义类
      if (this.enableTaskLists) {
        html = html.replace(
          /<li class="task-list-item([^"]*)">/g,
          '<li class="task-list-item$1 markdown-task-item">'
        )
        html = html.replace(
          /<input([^>]*class="task-list-item-checkbox[^"]*")([^>]*)>/g,
          '<input$1 markdown-task-checkbox"$2>'
        )
      }
      return html
    },

    // HTML转义函数
    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, (m) => map[m])
    },

    // 简化的渲染方法（作为后备方案）
    renderSimple(content) {
      if (!content) return ''

      try {
        // 创建一个最基础的 markdown-it 实例
        const simpleMarkdown = new MarkdownIt({
          html: true,
          breaks: true,
          linkify: true,
          typographer: true
        })

        return simpleMarkdown.render(content)
      } catch (error) {
        console.error('简化渲染失败:', error)
        return `<pre style="white-space: pre-wrap; font-family: inherit;">${this.escapeHtml(content)}</pre>`
      }
    },

    // 性能优化：缓存渲染结果
    getCachedRender(content) {
      // 简单的内存缓存，避免重复渲染相同内容
      if (!this._renderCache) {
        this._renderCache = new Map()
      }

      const cacheKey = content + JSON.stringify({
        highlight: this.enableHighlight,
        anchor: this.enableAnchor,
        taskLists: this.enableTaskLists,
        toc: this.enableToc
      })

      if (this._renderCache.has(cacheKey)) {
        return this._renderCache.get(cacheKey)
      }

      const result = this.renderedContent

      // 限制缓存大小，避免内存泄漏
      if (this._renderCache.size > 50) {
        const firstKey = this._renderCache.keys().next().value
        this._renderCache.delete(firstKey)
      }

      this._renderCache.set(cacheKey, result)
      return result
    }
  }
}
</script>

<style scoped>
/* Markdown渲染器样式 */
.markdown-renderer {
  line-height: 1.6;
  color: var(--text-primary);
  word-wrap: break-word;
}

/* 标题样式 */
.markdown-heading {
  margin: 20px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

/* 强制重置标题样式，防止被链接样式影响 */
.markdown-renderer h1,
.markdown-renderer h2,
.markdown-renderer h3,
.markdown-renderer h4,
.markdown-renderer h5,
.markdown-renderer h6 {
  text-decoration: none !important;
  color: inherit !important;
}

.markdown-renderer h1 a,
.markdown-renderer h2 a,
.markdown-renderer h3 a,
.markdown-renderer h4 a,
.markdown-renderer h5 a,
.markdown-renderer h6 a {
  text-decoration: none !important;
  color: inherit !important;
}

.markdown-h1 {
  font-size: 1.8em;
  margin: 24px 0 16px 0;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.markdown-h2 {
  font-size: 1.5em;
  margin: 20px 0 14px 0;
  color: var(--text-primary) !important;
  text-decoration: none !important;
}

.markdown-h3 {
  font-size: 1.3em;
  margin: 18px 0 12px 0;
  color: var(--primary-color) !important;
  text-decoration: none !important;
}

.markdown-h4 {
  font-size: 1.1em;
  margin: 16px 0 10px 0;
  color: var(--primary-color) !important;
  text-decoration: none !important;
}

.markdown-h5, .markdown-h6 {
  font-size: 1em;
  margin: 14px 0 8px 0;
  color: var(--text-secondary) !important;
  text-decoration: none !important;
}

/* 段落样式 */
.markdown-paragraph {
  margin: 14px 0;
  line-height: 1.7;
  text-align: justify;
}

/* 通用段落样式 */
.markdown-renderer p {
  margin: 14px 0;
  line-height: 1.7;
  text-align: justify;
}

/* 列表样式 */
.markdown-list {
  margin: 12px 0;
  padding-left: 0;
}

.markdown-list-item {
  margin: 6px 0;
  line-height: 1.6;
  list-style-position: outside;
  margin-left: 20px;
}

/* 嵌套列表样式 */
.markdown-list .markdown-list {
  margin: 6px 0;
  padding-left: 20px;
}

.markdown-list .markdown-list .markdown-list-item {
  margin-left: 0;
}

/* 有序列表和无序列表的特殊样式 */
.markdown-renderer ul {
  list-style-type: disc;
  padding-left: 20px;
}

.markdown-renderer ol {
  list-style-type: decimal;
  padding-left: 20px;
}

.markdown-renderer ul ul {
  list-style-type: circle;
  margin: 4px 0;
}

.markdown-renderer ul ul ul {
  list-style-type: square;
}

.markdown-renderer ol ol {
  list-style-type: lower-alpha;
  margin: 4px 0;
}

.markdown-renderer li {
  margin: 4px 0;
  line-height: 1.6;
}

/* 强调样式 */
.markdown-strong {
  font-weight: 700;
  color: var(--primary-color);
}

.markdown-em {
  font-style: italic;
  color: var(--text-secondary);
}

/* 行内代码样式 */
.inline-code {
  background: rgba(var(--primary-color-rgb), 0.1);
  color: var(--primary-color);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
}

/* 代码块样式 */
.markdown-renderer pre {
  background: #f8f9fa;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
  line-height: 1.5;
}

.markdown-renderer pre code {
  background: none;
  border: none;
  padding: 0;
  color: inherit;
}

/* 代码高亮样式 */
.hljs {
  background: #f8f9fa !important;
  color: #333;
}

/* 删除线样式 */
.markdown-del {
  text-decoration: line-through;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 链接样式 */
.markdown-link {
  color: var(--primary-color);
  text-decoration: none;
  border-bottom: 1px solid transparent;
  transition: all 0.2s ease;
}

.markdown-link:hover {
  border-bottom-color: var(--primary-color);
  text-decoration: none;
}

/* 图片样式 */
.markdown-image {
  max-width: 100%;
  height: auto;
  border-radius: 8px;
  margin: 12px 0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease;
}

.markdown-image:hover {
  transform: scale(1.02);
}

/* 引用块样式 */
.markdown-blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid var(--primary-color);
  background: rgba(var(--primary-color-rgb), 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--text-secondary);
}

.markdown-blockquote p {
  margin: 0;
}

/* 表格样式 */
.markdown-table-wrapper {
  overflow-x: auto;
  margin: 16px 0;
  border-radius: 8px;
  border: 1px solid var(--border-color);
}

.markdown-table {
  width: 100%;
  border-collapse: collapse;
  background: var(--bg-primary);
}

.markdown-table-row:nth-child(even) {
  background: rgba(var(--primary-color-rgb), 0.02);
}

.markdown-table-cell {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-color);
  text-align: left;
  vertical-align: top;
}

.markdown-table thead .markdown-table-cell {
  background: rgba(var(--primary-color-rgb), 0.1);
  font-weight: 600;
  color: var(--primary-color);
  border-bottom: 2px solid var(--primary-color);
}

/* 分隔线样式 */
.markdown-hr {
  margin: 24px 0;
  border: none;
  height: 2px;
  background: linear-gradient(to right, transparent, var(--border-color), transparent);
}

/* 任务列表样式 */
.markdown-task-item,
.task-list-item {
  display: flex;
  align-items: flex-start;
  margin: 8px 0;
  list-style: none;
  padding-left: 0;
}

.markdown-task-checkbox,
.task-list-item-checkbox {
  margin-right: 8px;
  margin-top: 2px;
  cursor: default;
  accent-color: var(--primary-color);
}

.markdown-task-label {
  flex: 1;
  cursor: default;
  line-height: 1.6;
}

.markdown-task-checkbox:checked + .markdown-task-label,
.task-list-item-checkbox:checked + * {
  text-decoration: line-through;
  color: var(--text-secondary);
  opacity: 0.7;
}

/* 锚点链接样式 */
.header-anchor {
  color: var(--primary-color);
  text-decoration: none;
  opacity: 0;
  margin-left: 8px;
  transition: opacity 0.2s ease;
}

.markdown-heading:hover .header-anchor {
  opacity: 1;
}

/* 目录样式 */
.markdown-toc {
  background: rgba(var(--primary-color-rgb), 0.05);
  border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.markdown-toc ul {
  margin: 0;
  padding-left: 20px;
  list-style: none;
}

.markdown-toc li {
  margin: 4px 0;
}

.markdown-toc a {
  color: var(--text-primary);
  text-decoration: none;
  transition: color 0.2s ease;
}

.markdown-toc a:hover {
  color: var(--primary-color);
}

/* 代码高亮增强样式 */
.hljs {
  background: #f8f9fa !important;
  color: #333 !important;
  border-radius: 8px;
  font-size: 0.9em;
  line-height: 1.5;
}

/* 数学公式样式（如果需要支持） */
.katex {
  font-size: 1.1em;
}

.katex-display {
  margin: 16px 0;
  text-align: center;
}

/* 脚注样式（如果需要支持） */
.footnote-ref {
  color: var(--primary-color);
  text-decoration: none;
  font-size: 0.8em;
  vertical-align: super;
}

.footnotes {
  margin-top: 32px;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  font-size: 0.9em;
}

.footnotes ol {
  padding-left: 20px;
}

.footnotes li {
  margin: 8px 0;
}

/* 警告框样式（自定义扩展） */
.markdown-alert {
  margin: 16px 0;
  padding: 12px 16px;
  border-radius: 8px;
  border-left: 4px solid;
}

.markdown-alert.info {
  background: rgba(59, 130, 246, 0.1);
  border-left-color: #3b82f6;
  color: #1e40af;
}

.markdown-alert.warning {
  background: rgba(245, 158, 11, 0.1);
  border-left-color: #f59e0b;
  color: #92400e;
}

.markdown-alert.error {
  background: rgba(239, 68, 68, 0.1);
  border-left-color: #ef4444;
  color: #dc2626;
}

.markdown-alert.success {
  background: rgba(34, 197, 94, 0.1);
  border-left-color: #22c55e;
  color: #166534;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .markdown-table-wrapper {
    font-size: 0.9em;
  }

  .markdown-table-cell {
    padding: 8px 12px;
  }

  .markdown-image {
    margin: 8px 0;
  }
}

/* 确保整体布局的一致性 */
.markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
}

/* 修复可能的样式冲突 */
.markdown-renderer * {
  box-sizing: border-box;
}

/* 确保中文字体显示 */
.markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
}

.markdown-blockquote {
  margin: 12px 0;
  padding: 8px 12px;
}

</style>
