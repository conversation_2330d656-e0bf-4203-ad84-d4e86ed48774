// 全局样式变量
:root {
  // 颜色变量
  --primary-color: #2563eb;
  --primary-color-rgb: 37, 99, 235; // RGB值用于透明度
  --primary-hover: #1d4ed8;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  
  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  
  // 文字颜色
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  
  // 边框颜色
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
  
  // 阴影
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  
  // 字体大小
  --font-xs: 12px;
  --font-sm: 14px;
  --font-base: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
}

// 浅色主题（默认）
[data-theme="light"] {
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;

  --text-primary: #1f2937;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;

  --border-color: #e5e7eb;
  --border-hover: #d1d5db;
}

// 深色主题
[data-theme="dark"] {
  --bg-primary: #1f2937;
  --bg-secondary: #111827;
  --bg-tertiary: #374151;

  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;

  --border-color: #374151;
  --border-hover: #4b5563;
}

// 字体大小主题
[data-font-size="small"] {
  --font-xs: 10px;
  --font-sm: 12px;
  --font-base: 14px;
  --font-lg: 16px;
  --font-xl: 18px;
  --font-2xl: 20px;
}

[data-font-size="large"] {
  --font-xs: 14px;
  --font-sm: 16px;
  --font-base: 18px;
  --font-lg: 20px;
  --font-xl: 22px;
  --font-2xl: 26px;
}

// 全局重置
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-base);
  line-height: 1.5;
}

#app {
  height: 100%;
}

// 滚动条样式
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
}

::-webkit-scrollbar-thumb {
  background: var(--text-tertiary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-secondary);
}

// 通用工具类
.flex {
  display: flex;
}

.flex-col {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.text-center {
  text-align: center;
}

.cursor-pointer {
  cursor: pointer;
}

.transition {
  transition: all 0.2s ease;
}

// 按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: var(--font-sm);
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.btn-primary {
    background-color: var(--primary-color);
    color: white;
    
    &:hover:not(:disabled) {
      background-color: var(--primary-hover);
    }
  }
  
  &.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border-color);
    
    &:hover:not(:disabled) {
      background-color: var(--bg-secondary);
      border-color: var(--border-hover);
    }
  }
  
  &.btn-sm {
    padding: 6px 12px;
    font-size: var(--font-xs);
  }
  
  &.btn-lg {
    padding: 12px 24px;
    font-size: var(--font-base);
  }
}

// 输入框样式
.input {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid var(--border-color);
  border-radius: 6px;
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-sm);
  transition: border-color 0.2s ease;
  
  &:focus {
    outline: none;
    border-color: var(--primary-color);
  }
  
  &::placeholder {
    color: var(--text-tertiary);
  }
}

// 卡片样式
.card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  box-shadow: var(--shadow-sm);
}

// 动画
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.3s ease;
}

// Element UI 主题覆盖
.el-button--primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);

  &:hover {
    background-color: var(--primary-hover);
    border-color: var(--primary-hover);
  }
}

.el-input__inner {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);

  &:focus {
    border-color: var(--primary-color);
  }
}

.el-textarea__inner {
  background-color: var(--bg-primary);
  border-color: var(--border-color);
  color: var(--text-primary);

  &:focus {
    border-color: var(--primary-color);
  }
}

.el-dialog {
  background-color: var(--bg-primary);

  .el-dialog__header {
    border-bottom: 1px solid var(--border-color);
  }

  .el-dialog__title {
    color: var(--text-primary);
  }
}

.el-tabs__item {
  color: var(--text-secondary);

  &.is-active {
    color: var(--primary-color);
  }
}

.el-tabs__active-bar {
  background-color: var(--primary-color);
}

.el-tabs__nav-wrap::after {
  background-color: var(--border-color);
}

.el-form-item__label {
  color: var(--text-primary);
}

.el-select .el-input__inner {
  cursor: pointer;
}

.el-radio__label {
  color: var(--text-primary);
}

.el-radio__input.is-checked .el-radio__inner {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.el-radio__inner {
  border-color: var(--border-color);
  background-color: var(--bg-primary);
}

.el-radio:hover .el-radio__inner {
  border-color: var(--primary-color);
}

.el-dropdown-menu {
  background-color: var(--bg-primary);
  border-color: var(--border-color);

  .el-dropdown-menu__item {
    color: var(--text-primary);

    &:hover {
      background-color: var(--bg-secondary);
    }
  }
}


