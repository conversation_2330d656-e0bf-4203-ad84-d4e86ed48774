<template>
  <div class="api-status" :class="statusClass">
    <i :class="statusIcon"></i>
    <span class="status-text">{{ statusText }}</span>
  </div>
</template>

<script>
export default {
  name: 'ApiStatus',
  data() {
    return {
      status: 'unknown', // unknown, online, offline, error
      lastCheck: null
    }
  },
  computed: {
    statusClass() {
      return `status-${this.status}`
    },
    
    statusIcon() {
      const icons = {
        unknown: 'el-icon-question',
        online: 'el-icon-success',
        offline: 'el-icon-error',
        error: 'el-icon-warning'
      }
      return icons[this.status] || icons.unknown
    },
    
    statusText() {
      const texts = {
        unknown: '检查中...',
        online: 'API在线',
        offline: 'API离线',
        error: 'API异常'
      }
      return texts[this.status] || texts.unknown
    }
  },
  async mounted() {
    await this.checkApiStatus()
    // 每30秒检查一次API状态
    this.statusTimer = setInterval(this.checkApiStatus, 30000)
  },
  beforeDestroy() {
    if (this.statusTimer) {
      clearInterval(this.statusTimer)
    }
  },
  methods: {
    async checkApiStatus() {
      try {
        const { healthCheck } = await import('@/api/common')
        await healthCheck()
        this.status = 'online'
        this.lastCheck = new Date()
      } catch (error) {
        if (error.code === 'NETWORK_ERROR' || error.message.includes('timeout')) {
          this.status = 'offline'
        } else {
          this.status = 'error'
        }
        this.lastCheck = new Date()
        console.warn('API状态检查失败:', error.message)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.api-status {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: var(--font-xs);
  transition: all 0.2s ease;
  
  &.status-unknown {
    background: var(--bg-tertiary);
    color: var(--text-tertiary);
  }
  
  &.status-online {
    background: rgba(16, 185, 129, 0.1);
    color: var(--success-color);
  }
  
  &.status-offline {
    background: rgba(239, 68, 68, 0.1);
    color: var(--danger-color);
  }
  
  &.status-error {
    background: rgba(245, 158, 11, 0.1);
    color: var(--warning-color);
  }
}

.status-text {
  font-weight: 500;
}
</style>
