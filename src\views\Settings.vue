<template>
  <div class="settings-container">
    <div class="settings-header">
      <h2 class="settings-title">
        <i class="el-icon-setting"></i>
        设置
      </h2>
    </div>
    
    <div class="settings-content">
      <el-tabs v-model="activeTab" class="settings-tabs">
        <!-- 外观设置 -->
        <el-tab-pane label="外观" name="appearance">
          <div class="settings-section">
            <h3 class="section-title">
              <i class="el-icon-view"></i>
              外观设置
            </h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">主题模式</div>
                <div class="label-desc">选择浅色或深色主题</div>
              </div>
              <div class="setting-control">
                <el-radio-group :value="theme" @input="updateSetting('theme', $event)">
                  <el-radio label="light">浅色</el-radio>
                  <el-radio label="dark">深色</el-radio>
                </el-radio-group>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">字体大小</div>
                <div class="label-desc">调整界面字体大小</div>
              </div>
              <div class="setting-control">
                <el-select :value="fontSize" @input="updateSetting('fontSize', $event)">
                  <el-option label="小" value="small"></el-option>
                  <el-option label="中" value="medium"></el-option>
                  <el-option label="大" value="large"></el-option>
                </el-select>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">语言</div>
                <div class="label-desc">选择界面语言</div>
              </div>
              <div class="setting-control">
                <el-select :value="language" @input="updateSetting('language', $event)">
                  <el-option label="中文" value="zh-CN"></el-option>
                  <el-option label="English" value="en-US"></el-option>
                </el-select>
              </div>
            </div>

            <!-- 调试组件 -->
            <!-- 主题调试功能已移除 -->
          </div>
        </el-tab-pane>

        <!-- AI设置 -->
        <el-tab-pane label="AI设置" name="ai">
          <div class="settings-section">
            <h3 class="section-title">
              <i class="el-icon-cpu"></i>
              AI设置
            </h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">API密钥</div>
                <div class="label-desc">配置AI服务的API密钥</div>
              </div>
              <div class="setting-control">
                <el-input
                  :value="apiKey"
                  type="password"
                  placeholder="sk-..."
                  show-password
                  @input="updateSetting('apiKey', $event)"
                />
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">AI模型</div>
                <div class="label-desc">选择使用的AI模型</div>
              </div>
              <div class="setting-control">
                <el-select :value="aiModel" @input="updateSetting('aiModel', $event)">
                  <el-option label="GPT-3.5" value="gpt-3.5-turbo"></el-option>
                  <el-option label="GPT-4" value="gpt-4"></el-option>
                  <el-option label="Claude" value="claude"></el-option>
                </el-select>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">自动保存对话</div>
                <div class="label-desc">自动保存对话历史到本地</div>
              </div>
              <div class="setting-control">
                <el-switch
                  :value="autoSave"
                  @input="updateSetting('autoSave', $event)"
                />
              </div>
            </div>

            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">API连接测试</div>
                <div class="label-desc">测试后台API接口连接状态</div>
              </div>
              <div class="setting-control">
                <el-button
                  type="primary"
                  size="small"
                  @click="goToApiTest"
                >
                  <i class="el-icon-link"></i>
                  测试API
                </el-button>
              </div>
            </div>

            <div v-if="isDevelopment" class="setting-item">
              <div class="setting-label">
                <div class="label-title">开发者测试工具</div>
                <div class="label-desc">测试各种功能组件（开发模式）</div>
              </div>
              <div class="setting-control">
                <el-button
                  type="success"
                  size="small"
                  @click="goToTimerTest"
                >
                  <i class="el-icon-time"></i>
                  计时器测试
                </el-button>
                <el-button
                  type="info"
                  size="small"
                  @click="goToLoadingTest"
                  style="margin-left: 8px;"
                >
                  <i class="el-icon-loading"></i>
                  加载状态测试
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
        
        <!-- 隐私与安全 -->
        <el-tab-pane label="隐私安全" name="privacy">
          <div class="settings-section">
            <h3 class="section-title">
              <i class="el-icon-lock"></i>
              隐私与安全
            </h3>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">数据加密</div>
                <div class="label-desc">启用本地数据加密存储</div>
              </div>
              <div class="setting-control">
                <el-switch
                  :value="dataEncryption"
                  @input="updateSetting('dataEncryption', $event)"
                />
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">清除历史记录</div>
                <div class="label-desc">删除所有对话历史和本地数据</div>
              </div>
              <div class="setting-control">
                <el-button
                  type="danger"
                  size="small"
                  @click="clearAllData"
                >
                  <i class="el-icon-delete"></i>
                  清除数据
                </el-button>
              </div>
            </div>
            
            <div class="setting-item">
              <div class="setting-label">
                <div class="label-title">重置设置</div>
                <div class="label-desc">恢复所有设置到默认值</div>
              </div>
              <div class="setting-control">
                <el-button
                  type="warning"
                  size="small"
                  @click="resetSettings"
                >
                  <i class="el-icon-refresh"></i>
                  重置设置
                </el-button>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
    
    <div class="settings-footer">
      <el-button type="primary" @click="saveSettings">
        <i class="el-icon-check"></i>
        保存设置
      </el-button>
    </div>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Settings',
  components: {
    // 组件已清理
  },
  data() {
    return {
      activeTab: 'appearance'
    }
  },
  computed: {
    ...mapGetters('settings', ['allSettings', 'theme', 'fontSize', 'language', 'apiKey', 'aiModel', 'autoSave', 'dataEncryption']),
    settings() {
      return { ...this.allSettings }
    },

    // 判断是否为开发环境
    isDevelopment() {
      return process.env.NODE_ENV === 'development'
    }
  },
  methods: {
    ...mapActions('settings', ['updateSettings', 'resetSettings', 'clearAllData']),
    
    updateSetting(key, value) {
      console.log('更新设置:', key, value)
      console.log('当前主题:', this.theme)

      this.updateSettings({ [key]: value })

      // 立即应用主题变化
      if (key === 'theme') {
        console.log('应用主题:', value)
        document.documentElement.setAttribute('data-theme', value)
        console.log('DOM主题属性:', document.documentElement.getAttribute('data-theme'))
      }
      if (key === 'fontSize') {
        document.documentElement.setAttribute('data-font-size', value)
      }
    },
    
    saveSettings() {
      this.$message.success('设置已保存')
    },
    
    async clearAllData() {
      try {
        await this.$confirm(
          '此操作将删除所有对话历史和本地数据，且无法恢复。确定要继续吗？',
          '警告',
          {
            confirmButtonText: '确定删除',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        this.clearAllData()
        this.$message.success('数据已清除')
      } catch (error) {
        // 用户取消
      }
    },
    
    async resetSettings() {
      try {
        await this.$confirm(
          '此操作将恢复所有设置到默认值，确定要继续吗？',
          '提示',
          {
            confirmButtonText: '确定重置',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )

        this.resetSettings()
        this.$message.success('设置已重置')
      } catch (error) {
        // 用户取消
      }
    },

    goToApiTest() {
      this.$router.push('/main/api-test')
    },

    goToTimerTest() {
      this.$router.push('/main/timer-test')
    },

    goToLoadingTest() {
      this.$router.push('/main/loading-test')
    }
  }
}
</script>

<style lang="scss" scoped>
.settings-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--bg-primary);
}

.settings-header {
  padding: 20px;
  border-bottom: 1px solid var(--border-color);
}

.settings-title {
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.settings-content {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
}

.settings-tabs {
  height: 100%;
  
  ::v-deep .el-tabs__content {
    height: calc(100% - 40px);
    overflow-y: auto;
  }
}

.settings-section {
  margin-bottom: 32px;
}

.section-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--border-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid var(--bg-tertiary);
  
  &:last-child {
    border-bottom: none;
  }
}

.setting-label {
  flex: 1;
  margin-right: 20px;
}

.label-title {
  font-size: var(--font-base);
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 4px;
}

.label-desc {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.setting-control {
  display: flex;
  align-items: center;
  min-width: 200px;
  justify-content: flex-end;
}

.settings-footer {
  padding: 20px;
  border-top: 1px solid var(--border-color);
  text-align: center;
}
</style>
