<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式 Markdown 解析修复测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .test-panel {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .panel-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .controls {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #409eff;
            color: white;
        }

        .btn-primary:hover {
            background: #337ecc;
        }

        .btn-success {
            background: #67c23a;
            color: white;
        }

        .btn-success:hover {
            background: #5daf34;
        }

        .btn-danger {
            background: #f56c6c;
            color: white;
        }

        .btn-danger:hover {
            background: #f45656;
        }

        .btn:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status {
            padding: 10px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }

        .output {
            min-height: 300px;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Markdown 样式 */
        .output h1, .output h2, .output h3 {
            color: #333;
            margin: 16px 0 10px 0;
            font-weight: 600;
        }

        .output h1 {
            font-size: 1.6em;
            border-bottom: 2px solid #eee;
            padding-bottom: 6px;
        }

        .output h2 {
            font-size: 1.4em;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
        }

        .output h3 {
            font-size: 1.2em;
            color: #409eff;
        }

        .output p {
            margin: 10px 0;
        }

        .output ul, .output ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .output li {
            margin: 4px 0;
        }

        .output strong {
            font-weight: 700;
            color: #409eff;
        }

        .output em {
            font-style: italic;
            color: #666;
        }

        .output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .output blockquote {
            margin: 12px 0;
            padding: 10px 12px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 6px 6px 0;
            font-style: italic;
            color: #666;
        }

        .comparison {
            background: #fff9e6;
            border: 1px solid #ffd700;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
        }

        .comparison h3 {
            color: #e6a23c;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .test-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🔧 流式 Markdown 解析修复测试</h1>
                <p>对比原始方法和优化方法的流式解析效果</p>
            </div>

            <div class="test-grid">
                <!-- 原始方法测试 -->
                <div class="test-panel">
                    <div class="panel-title">📊 原始方法（模拟问题）</div>
                    
                    <div class="controls">
                        <button class="btn btn-primary" @click="startOriginalTest" :disabled="originalStreaming">
                            开始原始测试
                        </button>
                        <button class="btn btn-danger" @click="stopOriginalTest" :disabled="!originalStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status">
                        状态: {{ originalStatus }}
                        <span v-if="originalStreaming" class="streaming-indicator">●</span>
                        <br>
                        进度: {{ originalProgress }}% | 字符: {{ originalContent.length }} | 渲染: {{ originalRenderCount }}次
                    </div>

                    <div class="output" v-html="originalRendered"></div>
                </div>

                <!-- 优化方法测试 -->
                <div class="test-panel">
                    <div class="panel-title">⚡ 优化方法（修复版）</div>
                    
                    <div class="controls">
                        <button class="btn btn-success" @click="startOptimizedTest" :disabled="optimizedStreaming">
                            开始优化测试
                        </button>
                        <button class="btn btn-danger" @click="stopOptimizedTest" :disabled="!optimizedStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status">
                        状态: {{ optimizedStatus }}
                        <span v-if="optimizedStreaming" class="streaming-indicator">●</span>
                        <br>
                        进度: {{ optimizedProgress }}% | 字符: {{ optimizedContent.length }} | 渲染: {{ optimizedRenderCount }}次
                    </div>

                    <div class="output" v-html="optimizedRendered"></div>
                </div>
            </div>

            <div class="comparison">
                <h3>🔍 对比分析</h3>
                <p><strong>原始方法问题</strong>：模拟 Vuex 状态管理延迟，computed 触发不及时</p>
                <p><strong>优化方法特点</strong>：强制更新机制 + 定时器 + 响应式优化</p>
                <p><strong>预期效果</strong>：优化方法应该有更及时的 Markdown 渲染更新</p>
            </div>
        </div>
    </div>

    <script>
        new Vue({
            el: '#app',
            data: {
                // Markdown 实例
                md: null,
                
                // 测试内容
                testContent: `# 🚀 流式 Markdown 测试

## 实时渲染效果测试

这是一个**流式输出**的测试，用来验证 Markdown 解析的实时性。

### ✨ 功能特点
- 📝 **实时解析**: 逐字符流式输出
- 🔄 **响应式更新**: Vue computed 自动触发
- ⚡ **性能优化**: 减少不必要的重渲染

### 📋 支持的语法
1. **标题** (H1-H6)
2. **粗体** 和 *斜体*
3. \`行内代码\`
4. 列表和编号列表

### 💻 代码示例

\`\`\`javascript
// 流式 Markdown 渲染
function renderMarkdown(content) {
    return markdownParser.render(content);
}
\`\`\`

> **重要**: 这个测试用来对比不同实现方法的效果差异。

---

**测试完成！** 🎉`,

                // 原始方法状态
                originalContent: '',
                originalStreaming: false,
                originalTimer: null,
                originalIndex: 0,
                originalRenderCount: 0,
                originalForceKey: 0,

                // 优化方法状态
                optimizedContent: '',
                optimizedStreaming: false,
                optimizedTimer: null,
                optimizedIndex: 0,
                optimizedRenderCount: 0,
                optimizedForceKey: 0,
                optimizedForceTimer: null
            },
            computed: {
                originalStatus() {
                    return this.originalStreaming ? '流式输出中' : '未开始'
                },
                originalProgress() {
                    return Math.round((this.originalIndex / this.testContent.length) * 100)
                },
                originalRendered() {
                    // 模拟原始方法的延迟问题
                    this.originalRenderCount++
                    
                    if (!this.md || !this.originalContent) return ''
                    
                    // 模拟延迟渲染（每3次才真正渲染）
                    if (this.originalRenderCount % 3 !== 0 && this.originalStreaming) {
                        return this.md.render(this.originalContent.substring(0, this.originalContent.length - 50))
                    }
                    
                    return this.md.render(this.originalContent)
                },
                optimizedStatus() {
                    return this.optimizedStreaming ? '流式输出中' : '未开始'
                },
                optimizedProgress() {
                    return Math.round((this.optimizedIndex / this.testContent.length) * 100)
                },
                optimizedRendered() {
                    // 优化方法：强制响应式更新
                    this.optimizedForceKey // 强制依赖
                    this.optimizedRenderCount++
                    
                    if (!this.md || !this.optimizedContent) return ''
                    
                    return this.md.render(this.optimizedContent)
                }
            },
            mounted() {
                this.initMarkdown()
            },
            beforeDestroy() {
                this.cleanup()
            },
            methods: {
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        console.log('✅ Markdown-it 初始化成功')
                    }
                },

                // 原始方法测试
                startOriginalTest() {
                    this.originalStreaming = true
                    this.originalIndex = 0
                    this.originalContent = ''
                    this.originalRenderCount = 0
                    
                    this.originalTimer = setInterval(() => {
                        if (this.originalIndex < this.testContent.length) {
                            const chunkSize = Math.floor(Math.random() * 3) + 1
                            const endIndex = Math.min(this.originalIndex + chunkSize, this.testContent.length)
                            
                            this.originalContent = this.testContent.substring(0, endIndex)
                            this.originalIndex = endIndex
                            
                            if (this.originalIndex >= this.testContent.length) {
                                this.stopOriginalTest()
                            }
                        }
                    }, 80)
                },

                stopOriginalTest() {
                    this.originalStreaming = false
                    if (this.originalTimer) {
                        clearInterval(this.originalTimer)
                        this.originalTimer = null
                    }
                },

                // 优化方法测试
                startOptimizedTest() {
                    this.optimizedStreaming = true
                    this.optimizedIndex = 0
                    this.optimizedContent = ''
                    this.optimizedRenderCount = 0
                    
                    // 启动强制更新定时器
                    this.optimizedForceTimer = setInterval(() => {
                        if (this.optimizedStreaming) {
                            this.optimizedForceKey++
                            this.$forceUpdate()
                        }
                    }, 50)
                    
                    this.optimizedTimer = setInterval(() => {
                        if (this.optimizedIndex < this.testContent.length) {
                            const chunkSize = Math.floor(Math.random() * 3) + 1
                            const endIndex = Math.min(this.optimizedIndex + chunkSize, this.testContent.length)
                            
                            this.optimizedContent = this.testContent.substring(0, endIndex)
                            this.optimizedIndex = endIndex
                            
                            // 立即强制更新
                            this.optimizedForceKey++
                            this.$nextTick(() => {
                                this.$forceUpdate()
                            })
                            
                            if (this.optimizedIndex >= this.testContent.length) {
                                this.stopOptimizedTest()
                            }
                        }
                    }, 80)
                },

                stopOptimizedTest() {
                    this.optimizedStreaming = false
                    if (this.optimizedTimer) {
                        clearInterval(this.optimizedTimer)
                        this.optimizedTimer = null
                    }
                    if (this.optimizedForceTimer) {
                        clearInterval(this.optimizedForceTimer)
                        this.optimizedForceTimer = null
                    }
                },

                cleanup() {
                    this.stopOriginalTest()
                    this.stopOptimizedTest()
                }
            }
        })
    </script>
</body>
</html>
