<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化版 Markdown 渲染器测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .input-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .markdown-input {
            height: 400px;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
        }

        .markdown-input:focus {
            border-color: #409eff;
        }

        .rendered-output {
            height: 400px;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .example-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .example-button {
            margin: 5px;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .example-button:hover {
            background: #337ecc;
        }

        .performance-info {
            margin-top: 20px;
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 8px;
            text-align: center;
        }

        .performance-info h3 {
            color: #409eff;
            margin-bottom: 10px;
        }

        .performance-stats {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
        }

        .stat-item {
            margin: 5px;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .stat-value {
            font-weight: bold;
            color: #409eff;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
        }

        .rendered-output a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>优化版 Markdown 渲染器测试</h1>
                <p>基于成功测试页面的实现，提供更稳定和高效的渲染</p>
            </div>

            <div class="test-container">
                <div class="input-section">
                    <div class="section-title">输入 Markdown：</div>
                    <textarea 
                        class="markdown-input" 
                        v-model="markdownContent"
                        placeholder="在这里输入 Markdown 内容..."
                        @input="updateOutput"
                    ></textarea>
                </div>
                <div class="output-section">
                    <div class="section-title">渲染结果：</div>
                    <div class="rendered-output" v-html="renderedHtml"></div>
                </div>
            </div>

            <div class="performance-info">
                <h3>性能统计</h3>
                <div class="performance-stats">
                    <div class="stat-item">
                        <div class="stat-label">渲染次数</div>
                        <div class="stat-value">{{ renderCount }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">缓存命中</div>
                        <div class="stat-value">{{ cacheHits }}</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">平均耗时</div>
                        <div class="stat-value">{{ averageTime }}ms</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-label">缓存大小</div>
                        <div class="stat-value">{{ cacheSize }}</div>
                    </div>
                </div>
            </div>

            <div class="example-buttons">
                <button class="example-button" @click="loadExample('basic')">基础语法</button>
                <button class="example-button" @click="loadExample('advanced')">高级功能</button>
                <button class="example-button" @click="loadExample('table')">表格示例</button>
                <button class="example-button" @click="loadExample('performance')">性能测试</button>
                <button class="example-button" @click="clearContent">清空内容</button>
                <button class="example-button" @click="clearCache">清除缓存</button>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                markdownContent: '',
                renderedHtml: '',
                md: null,
                renderCache: new Map(),
                renderCount: 0,
                cacheHits: 0,
                renderTimes: [],
                examples: {
                    basic: `# 基础 Markdown 语法测试

## 文本格式

这是一段普通文本，包含 **粗体文本** 和 *斜体文本*，还有 ~~删除线文本~~。

这里有一些行内代码：\`const hello = "world";\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 链接和引用

[Vue.js 官网](https://vuejs.org)

> 这是一个引用块
> 
> 可以包含多行内容

---

这是分隔线后的内容。`,
                    advanced: `# 高级 Markdown 功能

## 代码块

\`\`\`javascript
// Vue 组件示例
export default {
  name: 'MarkdownTest',
  data() {
    return {
      message: 'Hello, Markdown!'
    }
  },
  methods: {
    renderMarkdown(content) {
      return this.markdownIt.render(content)
    }
  }
}
\`\`\`

## 嵌套引用

> 第一级引用
> 
> > 第二级引用
> > 
> > > 第三级引用

## 复杂列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 另一个第三级项目
2. 回到第一级
3. 最后一个第一级项目`,
                    table: `# 表格示例

## 功能对比表格

| 解析器 | 性能 | 易用性 | 自定义性 | Vue 2 兼容 |
|--------|------|--------|----------|------------|
| 优化版组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |
| 原版组件 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |
| v-markdown 指令 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ✅ |

## 对齐示例

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 内容1  |   内容2   |  内容3 |
| 较长的内容 | 中等内容 | 短内容 |`,
                    performance: `# 性能测试内容

这是一个用于测试渲染性能的较长文档。

## 重复内容测试

${'### 小节 ' + Array.from({length: 10}, (_, i) => i + 1).map(i => `
#### 子标题 ${i}

这是第 ${i} 个段落，包含一些 **粗体** 和 *斜体* 文本。

\`\`\`javascript
// 代码块 ${i}
function test${i}() {
  console.log('测试函数 ${i}');
  return ${i};
}
\`\`\`

> 引用块 ${i}：这是一个测试引用，用于验证渲染性能。

- 列表项 ${i}.1
- 列表项 ${i}.2
- 列表项 ${i}.3

---
`).join('')}`
                }
            },
            computed: {
                averageTime() {
                    if (this.renderTimes.length === 0) return 0
                    const sum = this.renderTimes.reduce((a, b) => a + b, 0)
                    return Math.round(sum / this.renderTimes.length * 100) / 100
                },
                cacheSize() {
                    return this.renderCache.size
                }
            },
            mounted() {
                // 初始化 markdown-it
                this.initMarkdown()
                // 加载默认示例
                this.loadExample('basic')
            },
            methods: {
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                    } else {
                        console.error('markdown-it 库未加载')
                    }
                },
                updateOutput() {
                    if (!this.md) {
                        this.renderedHtml = '<p style="color: red;">Markdown-it 库未加载</p>'
                        return
                    }
                    
                    const startTime = performance.now()
                    const cacheKey = this.markdownContent
                    
                    // 检查缓存
                    if (this.renderCache.has(cacheKey)) {
                        this.renderedHtml = this.renderCache.get(cacheKey)
                        this.cacheHits++
                        return
                    }
                    
                    if (this.markdownContent) {
                        try {
                            this.renderedHtml = this.md.render(this.markdownContent)
                            
                            // 缓存结果
                            if (this.renderCache.size >= 50) {
                                const firstKey = this.renderCache.keys().next().value
                                this.renderCache.delete(firstKey)
                            }
                            this.renderCache.set(cacheKey, this.renderedHtml)
                            
                        } catch (error) {
                            this.renderedHtml = `<p style="color: red;">渲染错误: ${error.message}</p>`
                        }
                    } else {
                        this.renderedHtml = '<p style="color: #999;">请输入 Markdown 内容...</p>'
                    }
                    
                    // 记录性能数据
                    const endTime = performance.now()
                    const renderTime = endTime - startTime
                    this.renderTimes.push(renderTime)
                    if (this.renderTimes.length > 100) {
                        this.renderTimes.shift()
                    }
                    this.renderCount++
                },
                loadExample(type) {
                    this.markdownContent = this.examples[type] || this.examples.basic
                    this.updateOutput()
                },
                clearContent() {
                    this.markdownContent = ''
                    this.updateOutput()
                },
                clearCache() {
                    this.renderCache.clear()
                    this.cacheHits = 0
                    this.renderCount = 0
                    this.renderTimes = []
                    this.$forceUpdate()
                }
            }
        })
    </script>
</body>
</html>
