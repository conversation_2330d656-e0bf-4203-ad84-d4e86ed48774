<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 解析器测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .input-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .markdown-input {
            height: 400px;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
        }

        .markdown-input:focus {
            border-color: #409eff;
        }

        .rendered-output {
            height: 400px;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .example-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .example-button {
            margin: 5px;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
        }

        .example-button:hover {
            background: #337ecc;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
        }

        .rendered-output a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>Markdown 解析器测试</h1>
                <p>使用 v-markdown 指令进行实时 Markdown 解析</p>
            </div>

            <div class="test-container">
                <div class="input-section">
                    <div class="section-title">输入 Markdown：</div>
                    <textarea 
                        class="markdown-input" 
                        v-model="markdownContent"
                        placeholder="在这里输入 Markdown 内容..."
                    ></textarea>
                </div>
                <div class="output-section">
                    <div class="section-title">渲染结果：</div>
                    <div class="rendered-output" v-markdown="markdownContent"></div>
                </div>
            </div>

            <div class="example-buttons">
                <button class="example-button" @click="loadExample('basic')">基础语法</button>
                <button class="example-button" @click="loadExample('advanced')">高级功能</button>
                <button class="example-button" @click="loadExample('table')">表格示例</button>
                <button class="example-button" @click="clearContent">清空内容</button>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                markdownContent: '',
                examples: {
                    basic: `# 基础 Markdown 语法测试

## 文本格式

这是一段普通文本，包含 **粗体文本** 和 *斜体文本*，还有 ~~删除线文本~~。

这里有一些行内代码：\`const hello = "world";\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 链接和引用

[Vue.js 官网](https://vuejs.org)

> 这是一个引用块
> 
> 可以包含多行内容

---

这是分隔线后的内容。`,
                    advanced: `# 高级 Markdown 功能

## 代码块

\`\`\`javascript
// Vue 组件示例
export default {
  name: 'MarkdownTest',
  data() {
    return {
      message: 'Hello, Markdown!'
    }
  },
  methods: {
    renderMarkdown(content) {
      return this.markdownIt.render(content)
    }
  }
}
\`\`\`

## 嵌套引用

> 第一级引用
> 
> > 第二级引用
> > 
> > > 第三级引用

## 复杂列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 另一个第三级项目
2. 回到第一级
3. 最后一个第一级项目`,
                    table: `# 表格示例

## 功能对比表格

| 解析器 | 性能 | 易用性 | 自定义性 | Vue 2 兼容 |
|--------|------|--------|----------|------------|
| 自定义组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |
| v-markdown 指令 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |
| 原有组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ |

## 对齐示例

| 左对齐 | 居中对齐 | 右对齐 |
|:-------|:--------:|-------:|
| 内容1  |   内容2   |  内容3 |
| 较长的内容 | 中等内容 | 短内容 |`
                }
            },
            mounted() {
                // 初始化内容
                this.loadExample('basic')
            },
            methods: {
                loadExample(type) {
                    this.markdownContent = this.examples[type] || this.examples.basic
                },
                clearContent() {
                    this.markdownContent = ''
                }
            },
            // 注册 v-markdown 指令
            directives: {
                markdown: {
                    bind(el, binding) {
                        updateMarkdownContent(el, binding.value)
                    },
                    update(el, binding) {
                        updateMarkdownContent(el, binding.value)
                    }
                }
            }
        })

        // 独立的更新函数
        function updateMarkdownContent(el, content) {
            if (!window.markdownit) {
                el.innerHTML = '<p style="color: red;">Markdown-it 库未加载</p>'
                return
            }

            const md = window.markdownit({
                html: true,
                xhtmlOut: false,
                breaks: true,
                linkify: true,
                typographer: true
            })

            if (content) {
                try {
                    el.innerHTML = md.render(content)
                } catch (error) {
                    el.innerHTML = `<p style="color: red;">渲染错误: ${error.message}</p>`
                }
            } else {
                el.innerHTML = '<p style="color: #999;">请输入 Markdown 内容...</p>'
            }
        }
        })
    </script>
</body>
</html>
