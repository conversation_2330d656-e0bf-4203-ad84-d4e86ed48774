<template>
  <div class="transactions-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <i class="el-icon-document"></i>
          消费记录
        </h1>
        <p class="page-subtitle">查看您的充值和消费明细</p>
      </div>
    </div>

    <!-- 账户概览 -->
    <div class="account-overview">
      <div class="overview-card balance">
        <div class="card-icon">
          <i class="el-icon-wallet"></i>
        </div>
        <div class="card-content">
          <div class="card-label">当前余额</div>
          <div class="card-value">¥{{ balanceYuan }}</div>
        </div>
      </div>
      
      <div class="overview-card spent">
        <div class="card-icon">
          <i class="el-icon-shopping-cart-full"></i>
        </div>
        <div class="card-content">
          <div class="card-label">累计消费</div>
          <div class="card-value">¥{{ (userInfo.totalSpent / 100).toFixed(2) }}</div>
        </div>
      </div>
      
      <div class="overview-card vip">
        <div class="card-icon">
          <i class="el-icon-star-on"></i>
        </div>
        <div class="card-content">
          <div class="card-label">会员状态</div>
          <div class="card-value">{{ currentVipPlan.name }}</div>
          <div v-if="vipExpireTime" class="card-subtitle">
            到期时间：{{ formatDate(vipExpireTime) }}
          </div>
        </div>
      </div>
    </div>

    <!-- 筛选和操作 -->
    <div class="filter-bar">
      <div class="filter-group">
        <el-select v-model="filter.type" placeholder="记录类型" size="small">
          <el-option label="全部记录" value="all"></el-option>
          <el-option label="充值记录" value="recharge"></el-option>
          <el-option label="消费记录" value="consume"></el-option>
          <el-option label="VIP购买" value="vip"></el-option>
        </el-select>
        
        <el-date-picker
          v-model="filter.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
        ></el-date-picker>
      </div>
      
      <div class="action-group">
        <el-button type="primary" size="small" @click="$router.push('/main/recharge')">
          <i class="el-icon-plus"></i>
          充值
        </el-button>
        <el-button size="small" @click="exportRecords">
          <i class="el-icon-download"></i>
          导出
        </el-button>
      </div>
    </div>

    <!-- 交易记录表格 -->
    <div class="transactions-table">
      <el-table
        :data="filteredRecords"
        style="width: 100%"
        :empty-text="emptyText"
        v-loading="loading"
      >
        <el-table-column label="交易时间" min-width="180">
          <template slot-scope="scope">
            <div class="time-cell">
              <div>{{ formatDate(scope.row.createTime) }}</div>
              <div class="time-detail">{{ formatTime(scope.row.createTime) }}</div>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="交易类型" min-width="120">
          <template slot-scope="scope">
            <div class="type-cell">
              <el-tag :type="getTypeTag(scope.row.type)" size="small">
                {{ getTypeText(scope.row.type) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="交易内容" min-width="200">
          <template slot-scope="scope">
            <div class="description-cell">
              {{ scope.row.description }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="金额" min-width="120">
          <template slot-scope="scope">
            <div class="amount-cell" :class="scope.row.type">
              {{ scope.row.type === 'recharge' ? '+' : '-' }}
              ¥{{ (scope.row.amount / 100).toFixed(2) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="余额" min-width="120">
          <template slot-scope="scope">
            <div class="balance-cell">
              ¥{{ (scope.row.balance / 100).toFixed(2) }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column label="状态" min-width="100">
          <template slot-scope="scope">
            <div class="status-cell">
              <el-tag
                :type="getStatusType(scope.row.status)"
                size="small"
              >
                {{ getStatusText(scope.row.status) }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
      </el-table>
      
      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          background
          layout="prev, pager, next, sizes"
          :total="totalRecords"
          :page-size="pageSize"
          :current-page="currentPage"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        ></el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { getTransactionHistory } from '@/api/vip'

export default {
  name: 'Transactions',
  data() {
    return {
      loading: false,
      records: [],
      totalRecords: 0,
      currentPage: 1,
      pageSize: 10,
      filter: {
        type: 'all',
        dateRange: null
      },
      emptyText: '暂无交易记录'
    }
  },
  computed: {
    ...mapGetters('user', [
      'userInfo', 
      'balanceYuan', 
      'vipLevel', 
      'vipExpireTime',
      'currentVipPlan'
    ]),
    
    filteredRecords() {
      let result = [...this.records]
      
      // 按类型筛选
      if (this.filter.type !== 'all') {
        result = result.filter(record => record.type === this.filter.type)
      }
      
      // 按日期范围筛选
      if (this.filter.dateRange && this.filter.dateRange.length === 2) {
        const startDate = new Date(this.filter.dateRange[0])
        const endDate = new Date(this.filter.dateRange[1])
        endDate.setHours(23, 59, 59, 999) // 设置为当天结束时间
        
        result = result.filter(record => {
          const recordDate = new Date(record.createTime)
          return recordDate >= startDate && recordDate <= endDate
        })
      }
      
      return result
    }
  },
  async created() {
    await this.loadTransactions()
  },
  methods: {
    async loadTransactions() {
      this.loading = true
      
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 模拟交易记录数据
        this.records = [
          {
            id: '1',
            type: 'recharge',
            description: '账户充值',
            amount: 10000, // 100元，以分为单位
            balance: 10000,
            createTime: new Date().toISOString(),
            status: 'success',
            paymentMethod: '支付宝'
          },
          {
            id: '2',
            type: 'consume',
            description: '使用高级模型',
            amount: 500, // 5元
            balance: 9500,
            createTime: new Date(Date.now() - 3600000).toISOString(), // 1小时前
            status: 'success'
          },
          {
            id: '3',
            type: 'vip',
            description: '购买VIP会员（月付）',
            amount: 2900, // 29元
            balance: 6600,
            createTime: new Date(Date.now() - 7200000).toISOString(), // 2小时前
            status: 'success',
            paymentMethod: '余额支付'
          }
        ]
        
        this.totalRecords = this.records.length
      } catch (error) {
        console.error('加载交易记录失败:', error)
        this.emptyText = '加载失败，请重试'
      } finally {
        this.loading = false
      }
    },
    
    handleSizeChange(size) {
      this.pageSize = size
      this.loadTransactions()
    },
    
    handleCurrentChange(page) {
      this.currentPage = page
      this.loadTransactions()
    },
    
    formatDate(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleDateString('zh-CN')
    },
    
    formatTime(dateString) {
      if (!dateString) return ''
      const date = new Date(dateString)
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    },
    
    getTypeText(type) {
      const typeMap = {
        recharge: '充值',
        consume: '消费',
        vip: 'VIP购买',
        refund: '退款'
      }
      return typeMap[type] || '未知'
    },
    
    getTypeTag(type) {
      const tagMap = {
        recharge: 'success',
        consume: 'info',
        vip: 'warning',
        refund: ''
      }
      return tagMap[type] || ''
    },
    
    getStatusText(status) {
      const statusMap = {
        success: '成功',
        pending: '处理中',
        failed: '失败'
      }
      return statusMap[status] || '未知'
    },
    
    getStatusType(status) {
      const typeMap = {
        success: 'success',
        pending: 'warning',
        failed: 'danger'
      }
      return typeMap[status] || 'info'
    },
    
    exportRecords() {
      this.$message.success('导出成功，文件已下载')
    }
  }
}
</script>

<style lang="scss" scoped>
.transactions-container {
  width: 95%;
  max-width: 2000px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  margin-bottom: 30px;
  
  .page-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;
    
    i {
      color: var(--primary-color);
      margin-right: 10px;
    }
  }
  
  .page-subtitle {
    color: var(--text-secondary);
  }
}

.account-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.overview-card {
  border-radius: 12px;
  padding: 20px;
  display: flex;
  align-items: center;
  color: white;
  
  &.balance {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
  
  &.spent {
    background: linear-gradient(135deg, #2980b9 0%, #3498db 100%);
  }
  
  &.vip {
    background: linear-gradient(135deg, #f39c12 0%, #e67e22 100%);
  }
  
  .card-icon {
    font-size: 2.5rem;
    margin-right: 20px;
  }
  
  .card-label {
    font-size: 1rem;
    opacity: 0.9;
    margin-bottom: 5px;
  }
  
  .card-value {
    font-size: 1.8rem;
    font-weight: 700;
  }
  
  .card-subtitle {
    font-size: 0.9rem;
    opacity: 0.8;
    margin-top: 5px;
  }
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .filter-group {
    display: flex;
    gap: 15px;
  }
  
  .action-group {
    display: flex;
    gap: 10px;
  }
}

.transactions-table {
  background: var(--bg-primary);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  
  .time-cell {
    .time-detail {
      font-size: 0.9rem;
      color: var(--text-secondary);
      margin-top: 3px;
    }
  }
  
  .amount-cell {
    font-weight: 600;
    
    &.recharge {
      color: #27ae60;
    }
    
    &.consume, &.vip {
      color: #e74c3c;
    }
  }
  
  .balance-cell {
    color: var(--text-primary);
  }
  
  .description-cell {
    color: var(--text-primary);
  }
}

.pagination-container {
  padding: 20px;
  display: flex;
  justify-content: center;
}


</style>
