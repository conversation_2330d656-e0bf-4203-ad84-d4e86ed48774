<template>
  <div class="markdown-test-new">
    <div class="test-header">
      <h1>Markdown 解析器测试页面</h1>
      <p>测试不同的 Markdown 解析方式：vue-markdown-render 组件和 v-markdown 指令</p>
    </div>

    <div class="test-controls">
      <el-tabs v-model="activeTab" type="card">
        <el-tab-pane label="vue-markdown-render 组件" name="component">
          <div class="test-section">
            <h3>使用 vue-markdown-render 组件</h3>
            <div class="test-container">
              <div class="input-section">
                <h4>输入 Markdown：</h4>
                <el-input
                  type="textarea"
                  v-model="markdownContent"
                  :rows="15"
                  placeholder="在这里输入 Markdown 内容..."
                />
              </div>
              <div class="output-section">
                <h4>渲染结果：</h4>
                <div class="rendered-output">
                  <VueMarkdownCustom
                    :source="markdownContent"
                    :options="markdownOptions"
                    :plugins="markdownPlugins"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="v-markdown 指令" name="directive">
          <div class="test-section">
            <h3>使用 v-markdown 指令</h3>
            <div class="test-container">
              <div class="input-section">
                <h4>输入 Markdown：</h4>
                <el-input
                  type="textarea"
                  v-model="directiveContent"
                  :rows="15"
                  placeholder="在这里输入 Markdown 内容..."
                />
              </div>
              <div class="output-section">
                <h4>渲染结果：</h4>
                <div class="rendered-output" v-markdown="directiveContent"></div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <el-tab-pane label="原有组件对比" name="original">
          <div class="test-section">
            <h3>使用原有 MarkdownRenderer 组件</h3>
            <div class="test-container">
              <div class="input-section">
                <h4>输入 Markdown：</h4>
                <el-input
                  type="textarea"
                  v-model="originalContent"
                  :rows="15"
                  placeholder="在这里输入 Markdown 内容..."
                />
              </div>
              <div class="output-section">
                <h4>渲染结果：</h4>
                <div class="rendered-output">
                  <MarkdownRenderer
                    :content="originalContent"
                    :enable-html="true"
                    :enable-breaks="true"
                    :enable-linkify="true"
                    :enable-typographer="true"
                  />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <div class="preset-examples">
      <h3>预设示例</h3>
      <div class="example-buttons">
        <el-button @click="loadExample('basic')" type="primary">基础语法</el-button>
        <el-button @click="loadExample('advanced')" type="success">高级功能</el-button>
        <el-button @click="loadExample('code')" type="info">代码高亮</el-button>
        <el-button @click="loadExample('table')" type="warning">表格和任务</el-button>
      </div>
    </div>
  </div>
</template>

<script>
import MarkdownRenderer from '@/components/MarkdownRendererOptimized.vue'
import MarkdownIt from 'markdown-it'

// 自定义 VueMarkdown 组件，兼容 Vue 2
const VueMarkdownCustom = {
  name: 'VueMarkdownCustom',
  props: {
    source: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      md: null
    }
  },
  created() {
    this.initMarkdownIt()
  },
  computed: {
    renderedHtml() {
      if (!this.source || !this.md) return ''
      try {
        return this.md.render(this.source)
      } catch (error) {
        console.error('Markdown渲染失败:', error)
        return this.escapeHtml(this.source)
      }
    }
  },
  methods: {
    initMarkdownIt() {
      this.md = new MarkdownIt({
        html: true,
        xhtmlOut: false,
        breaks: true,
        linkify: true,
        typographer: true
      })
    },
    escapeHtml(text) {
      const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
      }
      return text.replace(/[&<>"']/g, (m) => map[m])
    }
  },
  template: '<div class="vue-markdown-custom" v-html="renderedHtml"></div>'
}

export default {
  name: 'MarkdownTestNew',
  components: {
    VueMarkdownCustom,
    MarkdownRenderer
  },
  data() {
    return {
      activeTab: 'component',
      markdownContent: '',
      directiveContent: '',
      originalContent: '',
      markdownOptions: {
        html: true,
        xhtmlOut: false,
        breaks: true,
        langPrefix: 'language-',
        linkify: true,
        typographer: true
      },
      markdownPlugins: [
        markdownItAnchor,
        [markdownItHighlightjs, { hljs: hljs }],
        markdownItTaskLists
      ],
      examples: {
        basic: `# 基础 Markdown 语法测试

## 文本格式

这是一段普通文本，包含 **粗体文本** 和 *斜体文本*，还有 ~~删除线文本~~。

这里有一些行内代码：\`const hello = "world";\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 链接和图片

[Vue.js 官网](https://vuejs.org)

![示例图片](https://via.placeholder.com/300x200/42b883/ffffff?text=Vue.js)

## 引用

> 这是一个引用块
> 
> 可以包含多行内容

---

这是分隔线后的内容。`,
        advanced: `# 高级 Markdown 功能

## 任务列表

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 待办事项

## 表格

| 功能 | vue-markdown-render | v-markdown 指令 | 原有组件 |
|------|-------------------|----------------|----------|
| 基础语法 | ✅ | ✅ | ✅ |
| 代码高亮 | ✅ | ✅ | ✅ |
| 任务列表 | ✅ | ✅ | ✅ |
| 表格支持 | ✅ | ✅ | ✅ |
| 自定义样式 | ⚠️ | ✅ | ✅ |

## 嵌套引用

> 第一级引用
> 
> > 第二级引用
> > 
> > > 第三级引用

## 复杂列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 另一个第三级项目
        - [x] 第四级任务列表（已完成）
        - [ ] 第四级任务列表（未完成）
2. 回到第一级
3. 最后一个第一级项目`,
        code: `# 代码高亮测试

## JavaScript 代码

\`\`\`javascript
// Vue 组件示例
export default {
  name: 'MarkdownTest',
  data() {
    return {
      message: 'Hello, Markdown!'
    }
  },
  methods: {
    renderMarkdown(content) {
      return this.markdownIt.render(content)
    }
  }
}
\`\`\`

## Python 代码

\`\`\`python
# Python 示例
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")
\`\`\`

## Java 代码

\`\`\`java
// Java 示例
public class MarkdownTest {
    public static void main(String[] args) {
        System.out.println("Hello, Markdown!");
        
        // 创建列表
        List<String> items = Arrays.asList(
            "vue-markdown-render",
            "v-markdown 指令",
            "原有组件"
        );
        
        items.forEach(System.out::println);
    }
}
\`\`\`

## CSS 代码

\`\`\`css
/* CSS 样式示例 */
.markdown-renderer {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.6;
  color: #333;
}

.markdown-renderer h1 {
  font-size: 2em;
  border-bottom: 2px solid #eee;
  padding-bottom: 0.3em;
}

.markdown-renderer code {
  background: #f4f4f4;
  padding: 2px 4px;
  border-radius: 3px;
}
\`\`\``,
        table: `# 表格和任务列表测试

## 功能对比表格

| 解析器 | 性能 | 易用性 | 自定义性 | Vue 2 兼容 | 推荐度 |
|--------|------|--------|----------|------------|--------|
| vue-markdown-render | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⚠️ | ⭐⭐⭐⭐ |
| v-markdown 指令 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐⭐ |
| 原有组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐ |

## 项目任务清单

### 开发任务
- [x] 安装 vue-markdown-render
- [x] 创建测试页面
- [x] 实现 v-markdown 指令
- [ ] 添加更多示例
- [ ] 优化样式
- [ ] 编写文档

### 测试任务
- [x] 基础语法测试
- [x] 代码高亮测试
- [x] 表格渲染测试
- [ ] 性能测试
- [ ] 兼容性测试
- [ ] 用户体验测试

### 部署任务
- [ ] 代码审查
- [ ] 单元测试
- [ ] 集成测试
- [ ] 生产环境部署

## 复杂表格

| 功能分类 | 具体功能 | vue-markdown-render | v-markdown | 原有组件 | 备注 |
|----------|----------|-------------------|------------|----------|------|
| **基础语法** | 标题 | ✅ | ✅ | ✅ | 1-6级标题 |
| | 段落 | ✅ | ✅ | ✅ | 自动换行 |
| | 强调 | ✅ | ✅ | ✅ | 粗体、斜体 |
| | 链接 | ✅ | ✅ | ✅ | 内联和引用 |
| **高级功能** | 代码块 | ✅ | ✅ | ✅ | 语法高亮 |
| | 表格 | ✅ | ✅ | ✅ | 对齐支持 |
| | 任务列表 | ✅ | ✅ | ✅ | 交互式复选框 |
| | 数学公式 | ❌ | 可扩展 | 可扩展 | 需要插件 |
| **自定义** | 样式控制 | 有限 | 完全 | 完全 | CSS 类名 |
| | 插件支持 | ✅ | ✅ | ✅ | markdown-it 插件 |`
      }
    }
  },
  mounted() {
    // 初始化内容
    this.loadExample('basic')
  },
  methods: {
    loadExample(type) {
      const content = this.examples[type] || this.examples.basic
      this.markdownContent = content
      this.directiveContent = content
      this.originalContent = content
    }
  },
  // 注册 v-markdown 指令
  directives: {
    markdown: {
      bind(el, binding) {
        this.updateMarkdown(el, binding.value)
      },
      update(el, binding) {
        this.updateMarkdown(el, binding.value)
      },
      updateMarkdown(el, content) {
        const md = new MarkdownIt({
          html: true,
          xhtmlOut: false,
          breaks: true,
          linkify: true,
          typographer: true
        })

        if (content) {
          try {
            el.innerHTML = md.render(content)
          } catch (error) {
            console.error('Markdown渲染失败:', error)
            el.innerHTML = `<p style="color: red;">渲染错误: ${error.message}</p>`
          }
        } else {
          el.innerHTML = '<p style="color: #999;">请输入 Markdown 内容...</p>'
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.markdown-test-new {
  padding: 20px;
  max-width: 1600px;
  margin: 0 auto;
  background: var(--bg-primary);
  min-height: 100vh;
}

.test-header {
  text-align: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid var(--border-color);

  h1 {
    color: var(--primary-color);
    font-size: 2.2em;
    margin: 0 0 10px 0;
    font-weight: 600;
  }

  p {
    color: var(--text-secondary);
    font-size: 1.1em;
    margin: 0;
    line-height: 1.6;
  }
}

.test-controls {
  margin-bottom: 30px;

  .el-tabs {
    background: var(--bg-primary);
  }

  .el-tab-pane {
    padding-top: 20px;
  }
}

.test-section {
  h3 {
    color: var(--primary-color);
    font-size: 1.4em;
    margin: 0 0 20px 0;
    font-weight: 600;
  }
}

.test-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  height: 600px;
}

.input-section, .output-section {
  display: flex;
  flex-direction: column;

  h4 {
    color: var(--text-primary);
    font-size: 1.1em;
    margin: 0 0 10px 0;
    font-weight: 600;
  }
}

.rendered-output {
  flex: 1;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 20px;
  background: var(--bg-secondary);
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.6;

  // 为 v-markdown 指令添加样式
  h1, h2, h3, h4, h5, h6 {
    color: var(--text-primary);
    margin: 20px 0 12px 0;
    font-weight: 600;
    line-height: 1.4;
  }

  h1 {
    font-size: 1.8em;
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 8px;
  }

  h2 {
    font-size: 1.5em;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 6px;
  }

  h3 {
    font-size: 1.3em;
    color: var(--primary-color);
  }

  p {
    margin: 14px 0;
    line-height: 1.7;
    text-align: justify;
  }

  ul, ol {
    margin: 12px 0;
    padding-left: 20px;
  }

  li {
    margin: 6px 0;
    line-height: 1.6;
  }

  strong {
    font-weight: 700;
    color: var(--primary-color);
  }

  em {
    font-style: italic;
    color: var(--text-secondary);
  }

  del {
    text-decoration: line-through;
    color: var(--text-secondary);
    opacity: 0.7;
  }

  code {
    background: rgba(var(--primary-color-rgb), 0.1);
    color: var(--primary-color);
    padding: 2px 6px;
    border-radius: 4px;
    font-family: 'Courier New', Monaco, monospace;
    font-size: 0.9em;
    border: 1px solid rgba(var(--primary-color-rgb), 0.2);
  }

  pre {
    background: #f8f9fa;
    border: 1px solid var(--border-color);
    border-radius: 8px;
    padding: 16px;
    margin: 16px 0;
    overflow-x: auto;
    font-family: 'Courier New', Monaco, monospace;
    font-size: 0.9em;
    line-height: 1.5;

    code {
      background: none;
      border: none;
      padding: 0;
      color: inherit;
    }
  }

  blockquote {
    margin: 16px 0;
    padding: 12px 16px;
    border-left: 4px solid var(--primary-color);
    background: rgba(var(--primary-color-rgb), 0.05);
    border-radius: 0 8px 8px 0;
    font-style: italic;
    color: var(--text-secondary);

    p {
      margin: 0;
    }
  }

  table {
    width: 100%;
    border-collapse: collapse;
    margin: 16px 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 8px;
    overflow: hidden;

    th, td {
      padding: 12px 16px;
      border-bottom: 1px solid var(--border-color);
      text-align: left;
      vertical-align: top;
    }

    th {
      background: rgba(var(--primary-color-rgb), 0.1);
      font-weight: 600;
      color: var(--primary-color);
      border-bottom: 2px solid var(--primary-color);
    }

    tr:nth-child(even) {
      background: rgba(var(--primary-color-rgb), 0.02);
    }
  }

  // 任务列表样式
  .task-list-item {
    display: flex;
    align-items: flex-start;
    margin: 8px 0;
    list-style: none;
    padding-left: 0;

    .task-list-item-checkbox {
      margin-right: 8px;
      margin-top: 2px;
      cursor: default;
      accent-color: var(--primary-color);
    }

    &.task-list-item-checkbox:checked + * {
      text-decoration: line-through;
      color: var(--text-secondary);
      opacity: 0.7;
    }
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    border-bottom: 1px solid transparent;
    transition: all 0.2s ease;

    &:hover {
      border-bottom-color: var(--primary-color);
    }
  }

  img {
    max-width: 100%;
    height: auto;
    border-radius: 8px;
    margin: 12px 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  hr {
    margin: 24px 0;
    border: none;
    height: 2px;
    background: linear-gradient(to right, transparent, var(--border-color), transparent);
  }
}

.preset-examples {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid var(--border-color);

  h3 {
    color: var(--primary-color);
    font-size: 1.3em;
    margin: 0 0 15px 0;
    font-weight: 600;
  }
}

.example-buttons {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;

  .el-button {
    margin: 0;
  }
}

// 代码高亮样式
.hljs {
  background: #f8f9fa !important;
  color: #333 !important;
  border-radius: 8px;
  font-size: 0.9em;
  line-height: 1.5;
}

// 响应式设计
@media (max-width: 1200px) {
  .markdown-test-new {
    padding: 15px;
  }

  .test-container {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .test-container {
    grid-template-columns: 1fr;
    height: auto;
    gap: 15px;
  }

  .rendered-output {
    min-height: 300px;
  }

  .example-buttons {
    flex-direction: column;

    .el-button {
      width: 100%;
    }
  }

  .test-header {
    h1 {
      font-size: 1.8em;
    }

    p {
      font-size: 1em;
    }
  }
}

// 确保 vue-markdown-render 组件的样式
:deep(.vue-markdown-render) {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
}
</style>
