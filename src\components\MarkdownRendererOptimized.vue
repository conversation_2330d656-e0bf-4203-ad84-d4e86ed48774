<template>
  <div class="markdown-renderer-optimized" v-html="renderedContent"></div>
</template>

<script>
import MarkdownIt from 'markdown-it'

/**
 * 优化版 Markdown 渲染器
 * 基于测试页面中成功的简单直接实现
 */
export default {
  name: 'MarkdownRendererOptimized',
  props: {
    // Markdown内容
    content: {
      type: String,
      default: ''
    },
    // 是否启用HTML标签
    enableHtml: {
      type: Boolean,
      default: true
    },
    // 是否启用换行转换
    enableBreaks: {
      type: Boolean,
      default: true
    },
    // 是否启用链接识别
    enableLinkify: {
      type: Boolean,
      default: true
    },
    // 是否启用排版优化
    enableTypographer: {
      type: Boolean,
      default: true
    },
    // 是否正在流式输出
    isStreaming: {
      type: Boolean,
      default: false
    },
    // 是否禁用缓存（用于流式输出）
    disableCache: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      md: null,
      renderCache: new Map(),
      renderCount: 0
    }
  },
  created() {
    this.initMarkdown()
  },
  watch: {
    // 监听配置变化，重新初始化
    enableHtml() {
      this.initMarkdown()
    },
    enableBreaks() {
      this.initMarkdown()
    },
    enableLinkify() {
      this.initMarkdown()
    },
    enableTypographer() {
      this.initMarkdown()
    },
    // 监听内容变化（用于调试流式输出）
    content: {
      handler(newContent, oldContent) {
        if (this.isStreaming && newContent !== oldContent) {
          // 强制触发重新渲染
          this.$nextTick(() => {
            this.$forceUpdate()
          })
        }
      },
      immediate: false
    }
  },
  computed: {
    renderedContent() {
      // 采用测试页面中成功的简单直接方法
      if (!this.md) {
        console.warn('⚠️ Markdown-it 实例未初始化')
        return '<p style="color: red;">Markdown-it 库未加载</p>'
      }

      if (!this.content) {
        return ''
      }

      try {
        // 直接渲染 - 简单有效的方法
        const result = this.md.render(this.content)

        // 调试信息（仅在流式输出时显示）
        if (this.isStreaming && this.content.length < 100) {
          console.log(`🔄 Markdown渲染: ${this.content.length} 字符`, this.content.substring(0, 50) + '...')
        }

        return result
      } catch (error) {
        console.error('❌ Markdown渲染失败:', error)
        console.log('📝 原始内容:', this.content)
        return `<p style="color: red;">渲染错误: ${error.message}</p>`
      }
    }
  },
  methods: {
    // 初始化 Markdown-it（采用测试页面中成功的配置）
    initMarkdown() {
      try {
        if (window.markdownit) {
          // 使用全局的 markdown-it（如果可用）
          this.md = window.markdownit({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
        } else {
          // 使用导入的 MarkdownIt
          this.md = new MarkdownIt({
            html: this.enableHtml,
            xhtmlOut: false,
            breaks: this.enableBreaks,
            linkify: this.enableLinkify,
            typographer: this.enableTypographer
          })
        }

        console.log('✅ Markdown-it 初始化成功')

      } catch (error) {
        console.error('❌ Markdown-it 初始化失败:', error)
        // 创建最基础的实例作为后备
        try {
          this.md = new MarkdownIt({
            html: true,
            breaks: true,
            linkify: true
          })
        } catch (fallbackError) {
          console.error('❌ 后备 Markdown-it 初始化也失败:', fallbackError)
          this.md = null
        }
      }
    }
  }
}
</script>

<style scoped>
.markdown-renderer-optimized {
  line-height: 1.6;
  color: var(--text-primary, #333);
  font-size: 14px;
}

/* 标题样式 */
.markdown-renderer-optimized >>> h1,
.markdown-renderer-optimized >>> h2,
.markdown-renderer-optimized >>> h3,
.markdown-renderer-optimized >>> h4,
.markdown-renderer-optimized >>> h5,
.markdown-renderer-optimized >>> h6 {
  color: var(--text-primary, #333);
  margin: 20px 0 12px 0;
  font-weight: 600;
  line-height: 1.4;
}

.markdown-renderer-optimized >>> h1 {
  font-size: 1.8em;
  border-bottom: 2px solid var(--border-light, #eee);
  padding-bottom: 8px;
}

.markdown-renderer-optimized >>> h2 {
  font-size: 1.5em;
  border-bottom: 1px solid var(--border-light, #eee);
  padding-bottom: 6px;
}

.markdown-renderer-optimized >>> h3 {
  font-size: 1.3em;
  color: var(--primary-color, #409eff);
}

/* 段落样式 */
.markdown-renderer-optimized >>> p {
  margin: 14px 0;
  line-height: 1.7;
}

/* 列表样式 */
.markdown-renderer-optimized >>> ul,
.markdown-renderer-optimized >>> ol {
  margin: 12px 0;
  padding-left: 20px;
}

.markdown-renderer-optimized >>> li {
  margin: 6px 0;
  line-height: 1.6;
}

/* 强调样式 */
.markdown-renderer-optimized >>> strong {
  font-weight: 700;
  color: var(--primary-color, #409eff);
}

.markdown-renderer-optimized >>> em {
  font-style: italic;
  color: var(--text-secondary, #666);
}

/* 代码样式 */
.markdown-renderer-optimized >>> code {
  background: rgba(64, 158, 255, 0.1);
  color: var(--primary-color, #409eff);
  padding: 2px 6px;
  border-radius: 4px;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.markdown-renderer-optimized >>> pre {
  background: var(--bg-secondary, #f8f9fa);
  border: 1px solid var(--border-light, #e9ecef);
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
  overflow-x: auto;
  font-family: 'Courier New', Monaco, monospace;
  font-size: 0.9em;
}

.markdown-renderer-optimized >>> pre code {
  background: none;
  color: inherit;
  padding: 0;
  border-radius: 0;
}

/* 引用块样式 */
.markdown-renderer-optimized >>> blockquote {
  margin: 16px 0;
  padding: 12px 16px;
  border-left: 4px solid var(--primary-color, #409eff);
  background: rgba(64, 158, 255, 0.05);
  border-radius: 0 8px 8px 0;
  font-style: italic;
  color: var(--text-secondary, #666);
}

/* 表格样式 */
.markdown-renderer-optimized >>> table {
  width: 100%;
  border-collapse: collapse;
  margin: 16px 0;
  border: 1px solid var(--border-light, #e9ecef);
  border-radius: 8px;
  overflow: hidden;
}

.markdown-renderer-optimized >>> th,
.markdown-renderer-optimized >>> td {
  padding: 12px 16px;
  border-bottom: 1px solid var(--border-light, #e9ecef);
  text-align: left;
}

.markdown-renderer-optimized >>> th {
  background: rgba(64, 158, 255, 0.1);
  font-weight: 600;
  color: var(--primary-color, #409eff);
}

/* 链接样式 */
.markdown-renderer-optimized >>> a {
  color: var(--primary-color, #409eff);
  text-decoration: none;
}

.markdown-renderer-optimized >>> a:hover {
  text-decoration: underline;
}

/* 分隔线样式 */
.markdown-renderer-optimized >>> hr {
  border: none;
  border-top: 2px solid var(--border-light, #eee);
  margin: 24px 0;
}

/* 错误消息样式 */
.markdown-renderer-optimized >>> .markdown-error {
  border: 1px solid #f56c6c;
  border-radius: 8px;
  padding: 16px;
  background: #fef0f0;
  margin: 16px 0;
}
</style>
