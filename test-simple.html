<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单 Markdown 测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-content {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: #f9f9f9;
        }
        
        /* 基本样式 */
        .markdown-heading {
            color: #333;
            margin: 16px 0 12px 0;
            font-weight: 600;
        }
        
        .markdown-h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #ddd;
            padding-bottom: 8px;
        }
        
        .markdown-h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #ddd;
            padding-bottom: 6px;
        }
        
        .markdown-paragraph {
            margin: 12px 0;
        }
        
        .markdown-list {
            margin: 12px 0;
            padding-left: 24px;
        }
        
        .markdown-strong {
            font-weight: 700;
            color: #0066cc;
        }
        
        .markdown-em {
            font-style: italic;
            color: #666;
        }
        
        .inline-code {
            background: rgba(0, 102, 204, 0.1);
            color: #0066cc;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }
        
        pre {
            background: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 16px;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
        }
        
        .markdown-task-item {
            display: flex;
            align-items: flex-start;
            margin: 8px 0;
            list-style: none;
            padding-left: 0;
        }
        
        .markdown-task-checkbox {
            margin-right: 8px;
            margin-top: 2px;
        }
        
        .markdown-task-checkbox:checked + .markdown-task-label {
            text-decoration: line-through;
            color: #666;
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <h1>简单 Markdown 渲染测试</h1>
    
    <div class="test-content" id="output">
        正在加载...
    </div>

    <script src="https://cdn.jsdelivr.net/npm/marked@4.3.0/marked.min.js"></script>
    <script>
        // 测试 Markdown 内容
        const testMarkdown = `# 测试标题

## 二级标题

这是一段普通文本，包含 **粗体** 和 *斜体* 文本，还有 \`行内代码\`。

### 列表测试

- 无序列表项 1
- 无序列表项 2
  - 子项目 2.1
  - 子项目 2.2

### 任务列表

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务

### 代码块

\`\`\`javascript
function hello() {
    console.log("Hello, World!");
    return "测试成功";
}
\`\`\`

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |
| 数据4 | 数据5 | 数据6 |

---

这是分隔线后的内容。`;

        // 配置 marked
        const renderer = new marked.Renderer();
        
        // 自定义渲染
        renderer.heading = (text, level) => {
            return `<h${level} class="markdown-heading markdown-h${level}">${text}</h${level}>`;
        };

        renderer.paragraph = (text) => {
            return `<p class="markdown-paragraph">${text}</p>`;
        };

        renderer.list = (body, ordered) => {
            const type = ordered ? 'ol' : 'ul';
            return `<${type} class="markdown-list">${body}</${type}>`;
        };

        renderer.listitem = (text) => {
            return `<li class="markdown-list-item">${text}</li>`;
        };

        renderer.strong = (text) => {
            return `<strong class="markdown-strong">${text}</strong>`;
        };

        renderer.em = (text) => {
            return `<em class="markdown-em">${text}</em>`;
        };

        renderer.codespan = (code) => {
            return `<code class="inline-code">${code}</code>`;
        };
        
        marked.setOptions({
            renderer: renderer,
            gfm: true,
            breaks: true
        });
        
        // 渲染并处理任务列表
        function processTaskLists(html) {
            return html.replace(
                /<li class="markdown-list-item">\s*\[([ xX])\]\s*(.*?)<\/li>/g,
                (_, checked, content) => {
                    const isChecked = checked.toLowerCase() === 'x';
                    const checkboxId = 'task_' + Math.random().toString(36).substring(2, 11);
                    return `<li class="markdown-task-item">
                        <input type="checkbox" id="${checkboxId}" class="markdown-task-checkbox" ${isChecked ? 'checked' : ''} disabled>
                        <label for="${checkboxId}" class="markdown-task-label">${content.trim()}</label>
                    </li>`;
                }
            );
        }
        
        // 渲染 Markdown
        let html = marked(testMarkdown);
        html = processTaskLists(html);
        
        document.getElementById('output').innerHTML = html;
    </script>
</body>
</html>
