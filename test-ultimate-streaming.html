<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>终极流式 Markdown 渲染测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .control-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .control-button.primary {
            background: #409eff;
            color: white;
        }

        .control-button.primary:hover {
            background: #337ecc;
        }

        .control-button.success {
            background: #67c23a;
            color: white;
        }

        .control-button.success:hover {
            background: #5daf34;
        }

        .control-button.danger {
            background: #f56c6c;
            color: white;
        }

        .control-button.danger:hover {
            background: #f45656;
        }

        .control-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status-info {
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-info h3 {
            color: #409eff;
            margin-bottom: 10px;
        }

        .status-stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .stat-item {
            margin: 5px;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .stat-value {
            font-weight: bold;
            color: #409eff;
        }

        .rendered-output {
            min-height: 400px;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #409eff;
            transition: width 0.3s ease;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
        }

        .rendered-output a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>🎯 终极流式 Markdown 渲染测试</h1>
                <p>彻底解决流式输出 Markdown 解析问题</p>
            </div>

            <div class="test-container">
                <div class="control-section">
                    <div class="section-title">控制面板</div>
                    
                    <div class="control-buttons">
                        <button class="control-button primary" @click="startStreaming" :disabled="isStreaming">
                            开始流式输出
                        </button>
                        <button class="control-button danger" @click="stopStreaming" :disabled="!isStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status-info">
                        <h3>状态信息</h3>
                        <div class="status-stats">
                            <div class="stat-item">
                                <div class="stat-label">流式状态</div>
                                <div class="stat-value">{{ streamStatus }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">进度</div>
                                <div class="stat-value">{{ progress }}%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">字符数</div>
                                <div class="stat-value">{{ currentContent.length }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">computed 触发次数</div>
                                <div class="stat-value">{{ computedCount }}</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button class="control-button primary" @click="loadExample('java')">Java 示例</button>
                        <button class="control-button primary" @click="loadExample('complex')">复杂示例</button>
                        <button class="control-button success" @click="testSpeed('fast')">快速测试</button>
                        <button class="control-button success" @click="testSpeed('slow')">慢速测试</button>
                    </div>
                </div>

                <div class="output-section">
                    <div class="section-title">
                        渲染结果
                        <span v-if="isStreaming" class="streaming-indicator">●</span>
                    </div>
                    <div class="rendered-output" v-html="renderedContent"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                currentContent: '',
                md: null,
                isStreaming: false,
                streamTimer: null,
                currentExample: '',
                currentIndex: 0,
                computedCount: 0,
                streamSpeed: 50,
                examples: {
                    java: `# Java的核心特点

## 跨平台性 ("Write Once, Run Anywhere")

• Java程序通过编译生成**字节码** (bytecode)，运行在 Java虚拟机 (JVM) 上。
- JVM 是平台相关的，但 Java程序本身是平台无关的，因此可以在任何安装了 JVM 的设备上运行。

### 面向对象编程

Java是一种**面向对象**的编程语言：

\`\`\`java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
\`\`\`

> Java的设计哲学是"简单、健壮、可移植"`,
                    complex: `# 🚀 人工智能技术全景

## 核心技术栈

### 1. 机器学习框架
- **TensorFlow**: Google开源的深度学习框架
- **PyTorch**: Facebook开源的动态计算图框架
- **Scikit-learn**: 经典机器学习算法库

### 2. 数据处理工具

| 工具 | 用途 | 优势 |
|------|------|------|
| Pandas | 数据分析 | 高效处理结构化数据 |
| NumPy | 数值计算 | 高性能数组操作 |
| Matplotlib | 数据可视化 | 丰富的图表类型 |

### 3. 代码示例

\`\`\`python
import tensorflow as tf
import numpy as np

# 创建简单的神经网络
model = tf.keras.Sequential([
    tf.keras.layers.Dense(128, activation='relu'),
    tf.keras.layers.Dropout(0.2),
    tf.keras.layers.Dense(10, activation='softmax')
])

# 编译模型
model.compile(
    optimizer='adam',
    loss='sparse_categorical_crossentropy',
    metrics=['accuracy']
)

print("模型创建成功！")
\`\`\`

> **重要提示**: 选择合适的框架对项目成功至关重要。

---

## 应用领域

1. **计算机视觉**
   - 图像识别
   - 目标检测
   - 人脸识别

2. **自然语言处理**
   - 机器翻译
   - 情感分析
   - 文本生成

3. **推荐系统**
   - 协同过滤
   - 内容推荐
   - 个性化服务`
                }
            },
            computed: {
                streamStatus() {
                    return this.isStreaming ? '流式输出中' : '未开始'
                },
                progress() {
                    if (!this.currentExample) return 0
                    return Math.round((this.currentIndex / this.currentExample.length) * 100)
                },
                renderedContent() {
                    // 增加计数器
                    this.computedCount++
                    
                    console.log('🔄 computed 被触发', {
                        count: this.computedCount,
                        contentLength: this.currentContent?.length || 0,
                        isStreaming: this.isStreaming,
                        hasMarkdown: !!this.md
                    })
                    
                    if (!this.md) {
                        return '<p style="color: red;">Markdown-it 库未加载</p>'
                    }
                    
                    if (!this.currentContent) {
                        return ''
                    }
                    
                    try {
                        const result = this.md.render(this.currentContent)
                        
                        if (this.isStreaming) {
                            console.log(`✅ 渲染成功: ${this.currentContent.length} 字符`)
                        }
                        
                        return result
                    } catch (error) {
                        console.error('渲染失败:', error)
                        return `<p style="color: red;">渲染错误: ${error.message}</p>`
                    }
                }
            },
            mounted() {
                this.initMarkdown()
                this.loadExample('java')
            },
            methods: {
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        console.log('✅ Markdown-it 初始化成功')
                    } else {
                        console.error('❌ markdown-it 库未加载')
                    }
                },
                
                startStreaming() {
                    if (!this.currentExample) {
                        this.loadExample('java')
                    }
                    
                    this.isStreaming = true
                    this.currentIndex = 0
                    this.currentContent = ''
                    this.computedCount = 0
                    
                    console.log('🚀 开始流式输出')
                    
                    this.streamTimer = setInterval(() => {
                        if (this.currentIndex < this.currentExample.length) {
                            // 模拟流式输出，每次添加1-3个字符
                            const chunkSize = Math.floor(Math.random() * 3) + 1
                            const endIndex = Math.min(this.currentIndex + chunkSize, this.currentExample.length)
                            
                            this.currentContent = this.currentExample.substring(0, endIndex)
                            this.currentIndex = endIndex
                            
                            if (this.currentIndex >= this.currentExample.length) {
                                this.stopStreaming()
                            }
                        }
                    }, this.streamSpeed)
                },
                
                stopStreaming() {
                    this.isStreaming = false
                    if (this.streamTimer) {
                        clearInterval(this.streamTimer)
                        this.streamTimer = null
                    }
                    
                    console.log('🛑 流式输出停止')
                },
                
                loadExample(type) {
                    this.stopStreaming()
                    this.currentExample = this.examples[type] || this.examples.java
                    this.currentContent = this.currentExample
                    this.currentIndex = this.currentExample.length
                    this.computedCount = 0
                },
                
                testSpeed(speed) {
                    this.stopStreaming()
                    this.streamSpeed = speed === 'fast' ? 20 : 100
                    this.startStreaming()
                }
            },
            
            beforeDestroy() {
                this.stopStreaming()
            }
        })
    </script>
</body>
</html>
