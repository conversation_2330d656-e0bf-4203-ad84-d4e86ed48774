/**
 * 网络连接检查工具
 * 提供网络状态检查、API连通性测试等功能
 */

/**
 * 检查网络连接状态
 * @returns {boolean} 是否在线
 */
export function isOnline() {
  return navigator.onLine
}

/**
 * 检查API服务器连通性
 * @param {string} url - API服务器地址
 * @param {number} timeout - 超时时间（毫秒）
 * @returns {Promise<boolean>} 是否连通
 */
export async function checkApiConnectivity(url, timeout = 5000) {
  try {
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), timeout)
    
    const response = await fetch(url, {
      method: 'HEAD',
      signal: controller.signal,
      mode: 'no-cors' // 避免CORS问题
    })
    
    clearTimeout(timeoutId)
    return true
  } catch (error) {
    console.warn('API连通性检查失败:', error.message)
    return false
  }
}

/**
 * 获取网络连接类型
 * @returns {string} 连接类型
 */
export function getConnectionType() {
  if ('connection' in navigator) {
    return navigator.connection.effectiveType || 'unknown'
  }
  return 'unknown'
}

/**
 * 监听网络状态变化
 * @param {Function} onOnline - 上线回调
 * @param {Function} onOffline - 离线回调
 */
export function watchNetworkStatus(onOnline, onOffline) {
  window.addEventListener('online', onOnline)
  window.addEventListener('offline', onOffline)
  
  // 返回清理函数
  return () => {
    window.removeEventListener('online', onOnline)
    window.removeEventListener('offline', onOffline)
  }
}

/**
 * 格式化网络错误信息
 * @param {Error} error - 错误对象
 * @returns {string} 格式化后的错误信息
 */
export function formatNetworkError(error) {
  if (!error) return '未知错误'
  
  // 网络连接错误
  if (error.code === 'NETWORK_ERROR' || error.message.includes('Network Error')) {
    return '网络连接失败，请检查网络设置'
  }
  
  // 超时错误
  if (error.code === 'ECONNABORTED' || error.message.includes('timeout')) {
    return '请求超时，请稍后重试'
  }
  
  // 服务器错误
  if (error.response) {
    const status = error.response.status
    switch (status) {
      case 404:
        return '请求的接口不存在'
      case 500:
        return '服务器内部错误'
      case 502:
        return '网关错误，服务器可能未启动'
      case 503:
        return '服务暂时不可用'
      default:
        return `服务器错误 (${status})`
    }
  }
  
  return error.message || '请求失败'
}
