import Vue from 'vue'
import VueRouter from 'vue-router'
import Login from '@/views/Login.vue'
import Main from '@/views/Main.vue'
import Chat from '@/views/Chat.vue'
import Settings from '@/views/Settings.vue'
import Profile from '@/views/Profile.vue'
import StreamTest from '@/views/StreamTest.vue'
import VipPlans from '@/views/VipPlans.vue'
import Recharge from '@/views/Recharge.vue'
import Transactions from '@/views/Transactions.vue'
import MarkdownTest from '@/views/MarkdownTest.vue'
import MarkdownTestNew from '@/views/MarkdownTestNew.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/login',
    name: 'Login',
    component: Login
  },
  {
    path: '/main',
    name: 'Main',
    component: Main,
    children: [
      {
        path: '',
        redirect: 'chat'
      },
      {
        path: 'chat/:id?',
        name: 'Chat',
        component: Chat
      },
      {
        path: 'settings',
        name: 'Settings',
        component: Settings
      },
      {
        path: 'profile',
        name: 'Profile',
        component: Profile
      },
      {
        path: 'stream-test',
        name: 'StreamTest',
        component: StreamTest
      },
      {
        path: 'vip-plans',
        name: 'VipPlans',
        component: VipPlans
      },
      {
        path: 'recharge',
        name: 'Recharge',
        component: Recharge
      },
      {
        path: 'transactions',
        name: 'Transactions',
        component: Transactions
      },
      {
        path: 'markdown-test',
        name: 'MarkdownTest',
        component: MarkdownTest
      },
      {
        path: 'markdown-test-new',
        name: 'MarkdownTestNew',
        component: MarkdownTestNew
      }
    ]
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const token = localStorage.getItem('token')
  
  if (to.path === '/login') {
    if (token) {
      next('/main')
    } else {
      next()
    }
  } else {
    if (token) {
      next()
    } else {
      next('/login')
    }
  }
})

export default router
