<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 渲染器测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        .input-section, .output-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
        }
        textarea {
            width: 100%;
            height: 400px;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            resize: vertical;
        }
        .output {
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 15px;
            background: #f9f9f9;
            min-height: 400px;
            overflow-y: auto;
        }
        h1, h2, h3 {
            color: #333;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 0;
        }
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <h1>Markdown 渲染器测试</h1>
    
    <div class="container">
        <div class="input-section">
            <h3>输入 Markdown</h3>
            <textarea id="markdownInput" placeholder="在这里输入 Markdown 内容...">
# 测试标题

## 二级标题

这是一段普通文本，包含 **粗体** 和 *斜体* 文本。

### 列表测试

- 无序列表项 1
- 无序列表项 2
  - 子项目 2.1
  - 子项目 2.2

### 任务列表

- [x] 已完成的任务
- [ ] 未完成的任务

### 代码块

```javascript
function hello() {
    console.log("Hello, World!");
}
```

### 表格

| 列1 | 列2 | 列3 |
|-----|-----|-----|
| 数据1 | 数据2 | 数据3 |

### 引用

> 这是一个引用块
> 包含多行内容

### 链接

[测试链接](https://www.example.com)

---

这是分隔线后的内容。
            </textarea>
            <button class="test-button" onclick="renderMarkdown()">渲染 Markdown</button>
        </div>
        
        <div class="output-section">
            <h3>渲染结果</h3>
            <div id="output" class="output">
                点击"渲染 Markdown"按钮查看结果
            </div>
        </div>
    </div>

    <!-- 引入 markdown-it 和相关插件 -->
    <script src="https://cdn.jsdelivr.net/npm/markdown-it@14.1.0/dist/markdown-it.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/markdown-it-task-lists@2.1.1/dist/markdown-it-task-lists.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/lib/core.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/lib/languages/javascript.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/lib/languages/python.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/highlight.js@11.11.1/lib/languages/java.min.js"></script>
    
    <script>
        // 注册语言
        hljs.registerLanguage('javascript', window.hljsJavascript);
        hljs.registerLanguage('python', window.hljsPython);
        hljs.registerLanguage('java', window.hljsJava);
        
        // 创建 markdown-it 实例
        const md = window.markdownit({
            html: true,
            xhtmlOut: false,
            breaks: true,
            linkify: true,
            typographer: true,
            highlight: function (str, lang) {
                if (lang && hljs.getLanguage(lang)) {
                    try {
                        return hljs.highlight(str, { language: lang }).value;
                    } catch (__) {}
                }
                return '';
            }
        });
        
        // 使用任务列表插件
        md.use(window.markdownItTaskLists, { enabled: true });
        
        function renderMarkdown() {
            const input = document.getElementById('markdownInput').value;
            const output = document.getElementById('output');
            
            try {
                const html = md.render(input);
                output.innerHTML = html;
            } catch (error) {
                output.innerHTML = `<p style="color: red;">渲染错误: ${error.message}</p>`;
            }
        }
        
        // 页面加载时自动渲染一次
        window.onload = function() {
            renderMarkdown();
        };
    </script>
</body>
</html>
