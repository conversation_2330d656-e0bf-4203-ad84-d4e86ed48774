<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复后的流式 Markdown 测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .control-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
        }

        .control-button.primary {
            background: #409eff;
            color: white;
        }

        .control-button.primary:hover {
            background: #337ecc;
        }

        .control-button.success {
            background: #67c23a;
            color: white;
        }

        .control-button.success:hover {
            background: #5daf34;
        }

        .control-button.warning {
            background: #e6a23c;
            color: white;
        }

        .control-button.warning:hover {
            background: #cf9236;
        }

        .control-button.danger {
            background: #f56c6c;
            color: white;
        }

        .control-button.danger:hover {
            background: #f45656;
        }

        .control-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status-info {
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .status-info h3 {
            color: #409eff;
            margin-bottom: 10px;
        }

        .status-stats {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .stat-item {
            margin: 5px;
            padding: 8px 12px;
            background: white;
            border-radius: 4px;
            font-size: 14px;
        }

        .stat-label {
            color: #666;
            font-size: 12px;
        }

        .stat-value {
            font-weight: bold;
            color: #409eff;
        }

        .rendered-output {
            min-height: 400px;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        .progress-bar {
            width: 100%;
            height: 4px;
            background: #f0f0f0;
            border-radius: 2px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #409eff;
            transition: width 0.3s ease;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
        }

        .rendered-output a:hover {
            text-decoration: underline;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>修复后的流式 Markdown 渲染测试</h1>
                <p>验证简化版组件的流式渲染效果</p>
            </div>

            <div class="test-container">
                <div class="control-section">
                    <div class="section-title">控制面板</div>
                    
                    <div class="control-buttons">
                        <button class="control-button primary" @click="startStreaming" :disabled="isStreaming">
                            开始流式输出
                        </button>
                        <button class="control-button danger" @click="stopStreaming" :disabled="!isStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status-info">
                        <h3>状态信息</h3>
                        <div class="status-stats">
                            <div class="stat-item">
                                <div class="stat-label">流式状态</div>
                                <div class="stat-value">{{ streamStatus }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">进度</div>
                                <div class="stat-value">{{ progress }}%</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">字符数</div>
                                <div class="stat-value">{{ currentContent.length }}</div>
                            </div>
                            <div class="stat-item">
                                <div class="stat-label">渲染次数</div>
                                <div class="stat-value">{{ renderCount }}</div>
                            </div>
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" :style="{ width: progress + '%' }"></div>
                        </div>
                    </div>

                    <div class="control-buttons">
                        <button class="control-button primary" @click="loadExample('ai')">AI 回复示例</button>
                        <button class="control-button primary" @click="loadExample('code')">代码示例</button>
                        <button class="control-button warning" @click="clearContent">清空内容</button>
                    </div>
                </div>

                <div class="output-section">
                    <div class="section-title">
                        渲染结果
                        <span v-if="isStreaming" class="streaming-indicator">●</span>
                    </div>
                    <div class="rendered-output" v-html="renderedHtml"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                currentContent: '',
                renderedHtml: '',
                md: null,
                isStreaming: false,
                streamTimer: null,
                currentExample: '',
                currentIndex: 0,
                renderCount: 0,
                examples: {
                    ai: `# 人工智能技术概述

人工智能（Artificial Intelligence，简称AI）是指由人创造的能够感知环境、学习知识、逻辑推理和执行任务的科学技术。

## 主要技术领域

### 1. 机器学习
- **监督学习**：使用标记数据训练模型
- **无监督学习**：从未标记数据中发现模式
- **强化学习**：通过奖励机制学习最优策略

### 2. 深度学习
深度学习是机器学习的一个子集，使用多层神经网络：

\`\`\`python
import tensorflow as tf

# 创建简单的神经网络
model = tf.keras.Sequential([
    tf.keras.layers.Dense(128, activation='relu'),
    tf.keras.layers.Dense(10, activation='softmax')
])
\`\`\`

### 3. 自然语言处理
- 文本分析和理解
- 机器翻译
- 对话系统

> "人工智能将是人类历史上最重要的技术革命之一。" —— 专家观点

## 应用场景

| 领域 | 应用 | 成熟度 |
|------|------|--------|
| 医疗 | 诊断辅助 | ⭐⭐⭐⭐ |
| 交通 | 自动驾驶 | ⭐⭐⭐ |
| 金融 | 风险评估 | ⭐⭐⭐⭐⭐ |

---

**总结**：AI技术正在快速发展，将深刻改变我们的生活和工作方式。`,
                    code: `# 代码示例集合

## JavaScript 异步编程

### Promise 示例
\`\`\`javascript
function fetchData() {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            resolve('数据获取成功');
        }, 1000);
    });
}

// 使用 async/await
async function getData() {
    try {
        const result = await fetchData();
        console.log(result);
    } catch (error) {
        console.error('错误:', error);
    }
}
\`\`\`

### Vue.js 组件
\`\`\`vue
<template>
  <div class="markdown-renderer">
    <div v-html="renderedContent"></div>
  </div>
</template>

<script>
export default {
  name: 'MarkdownRenderer',
  props: {
    content: String,
    isStreaming: Boolean
  },
  computed: {
    renderedContent() {
      return this.md.render(this.content);
    }
  }
}
</script>
\`\`\`

## Python 数据处理

\`\`\`python
import pandas as pd
import numpy as np

# 创建数据框
data = {
    'name': ['Alice', 'Bob', 'Charlie'],
    'age': [25, 30, 35],
    'city': ['北京', '上海', '深圳']
}

df = pd.DataFrame(data)
print(df.head())
\`\`\`

> **提示**：这些代码示例展示了现代编程的最佳实践。`
                }
            },
            computed: {
                streamStatus() {
                    return this.isStreaming ? '流式输出中' : '未开始'
                },
                progress() {
                    if (!this.currentExample) return 0
                    return Math.round((this.currentIndex / this.currentExample.length) * 100)
                }
            },
            mounted() {
                this.initMarkdown()
                this.loadExample('ai')
            },
            methods: {
                // 初始化 Markdown-it（使用与修复后组件相同的方法）
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        console.log('✅ Markdown-it 初始化成功')
                    } else {
                        console.error('❌ markdown-it 库未加载')
                    }
                },
                
                // 更新输出（使用与修复后组件相同的简单方法）
                updateOutput() {
                    if (!this.md) {
                        this.renderedHtml = '<p style="color: red;">Markdown-it 库未加载</p>'
                        return
                    }
                    
                    if (!this.currentContent) {
                        this.renderedHtml = ''
                        return
                    }
                    
                    this.renderCount++
                    
                    try {
                        // 直接渲染 - 简单有效的方法
                        this.renderedHtml = this.md.render(this.currentContent)
                    } catch (error) {
                        console.error('Markdown渲染失败:', error)
                        this.renderedHtml = `<p style="color: red;">渲染错误: ${error.message}</p>`
                    }
                },
                
                startStreaming() {
                    if (!this.currentExample) {
                        this.loadExample('ai')
                    }
                    
                    this.isStreaming = true
                    this.currentIndex = 0
                    this.currentContent = ''
                    this.renderCount = 0
                    
                    this.streamTimer = setInterval(() => {
                        if (this.currentIndex < this.currentExample.length) {
                            // 模拟流式输出，每次添加1-3个字符
                            const chunkSize = Math.floor(Math.random() * 3) + 1
                            const endIndex = Math.min(this.currentIndex + chunkSize, this.currentExample.length)
                            
                            this.currentContent = this.currentExample.substring(0, endIndex)
                            this.currentIndex = endIndex
                            
                            this.updateOutput()
                            
                            if (this.currentIndex >= this.currentExample.length) {
                                this.stopStreaming()
                            }
                        }
                    }, 50) // 每50ms更新一次
                },
                
                stopStreaming() {
                    this.isStreaming = false
                    if (this.streamTimer) {
                        clearInterval(this.streamTimer)
                        this.streamTimer = null
                    }
                    
                    // 流式完成后，确保最终渲染是正确的
                    this.$nextTick(() => {
                        this.updateOutput()
                    })
                },
                
                loadExample(type) {
                    this.stopStreaming()
                    this.currentExample = this.examples[type] || this.examples.ai
                    this.currentContent = this.currentExample
                    this.currentIndex = this.currentExample.length
                    this.updateOutput()
                },
                
                clearContent() {
                    this.stopStreaming()
                    this.currentContent = ''
                    this.currentExample = ''
                    this.currentIndex = 0
                    this.updateOutput()
                }
            },
            
            beforeDestroy() {
                this.stopStreaming()
            }
        })
    </script>
</body>
</html>
