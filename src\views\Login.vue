<template>
  <div class="login-container">
    <div class="login-card">
      <div class="login-header">
        <div class="login-logo">
          <i class="el-icon-robot"></i>
        </div>
        <h1 class="login-title">AI助手</h1>
        <p class="login-subtitle">智能对话，高效助手</p>
      </div>
      
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @submit.native.prevent="handleLogin"
      >
        <el-form-item prop="email">
          <el-input
            v-model="loginForm.email"
            placeholder="请输入邮箱地址"
            prefix-icon="el-icon-message"
            size="large"
          />
        </el-form-item>
        
        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="el-icon-lock"
            size="large"
            show-password
            @keyup.enter.native="handleLogin"
          />
        </el-form-item>
        
        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-btn"
            :loading="loading"
            @click="handleLogin"
          >
            <i class="el-icon-right"></i>
            登录
          </el-button>
        </el-form-item>
      </el-form>
      
      <div class="login-footer">
        <div class="demo-tips">
          <p class="demo-tip">
            <i class="el-icon-info"></i>
            <strong>演示账号（模拟登录）：</strong>
          </p>
          <div class="demo-accounts">
            <div class="demo-account">
              <span class="account-label">演示账号：</span>
              <span class="account-info"><EMAIL> / 123456</span>
            </div>
            <div class="demo-account">
              <span class="account-label">测试账号：</span>
              <span class="account-info"><EMAIL> / 123456</span>
            </div>
            <div class="demo-account">
              <span class="account-label">管理账号：</span>
              <span class="account-info"><EMAIL> / admin123</span>
            </div>
          </div>
          <p class="demo-note">
            <i class="el-icon-warning"></i>
            当前为演示模式，登录功能为模拟实现
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'

export default {
  name: 'Login',
  data() {
    return {
      loading: false,
      loginForm: {
        email: '',
        password: ''
      },
      loginRules: {
        email: [
          { required: true, message: '请输入邮箱地址', trigger: 'blur' },
          { type: 'email', message: '请输入正确的邮箱格式', trigger: 'blur' }
        ],
        password: [
          { required: true, message: '请输入密码', trigger: 'blur' },
          { min: 6, message: '密码长度不能少于6位', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    ...mapActions('user', ['login']),
    
    async handleLogin() {
      try {
        const valid = await this.$refs.loginForm.validate()
        if (!valid) return
        
        this.loading = true
        const result = await this.login(this.loginForm)
        
        if (result.success) {
          this.$message.success('登录成功')
          this.$router.push('/main')
        } else {
          this.$message.error(result.message || '登录失败')
        }
      } catch (error) {
        this.$message.error('登录失败，请重试')
      } finally {
        this.loading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-card {
  width: 100%;
  max-width: 400px;
  background: var(--bg-primary);
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  padding: 40px;
  animation: fadeIn 0.5s ease;
}

.login-header {
  text-align: center;
  margin-bottom: 30px;
}

.login-logo {
  font-size: 48px;
  color: var(--primary-color);
  margin-bottom: 16px;
}

.login-title {
  font-size: var(--font-2xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.login-subtitle {
  color: var(--text-secondary);
  font-size: var(--font-sm);
}

.login-form {
  .el-form-item {
    margin-bottom: 20px;
  }
}

.login-btn {
  width: 100%;
  height: 44px;
  font-size: var(--font-base);
  font-weight: 600;
}

.login-footer {
  margin-top: 20px;
  text-align: center;
}

.demo-tips {
  text-align: left;
}

.demo-tip {
  color: var(--text-secondary);
  font-size: var(--font-sm);
  margin-bottom: 12px;

  i {
    margin-right: 6px;
    color: var(--primary-color);
  }

  strong {
    color: var(--text-primary);
  }
}

.demo-accounts {
  background: var(--bg-secondary);
  border-radius: 6px;
  padding: 12px;
  margin-bottom: 12px;
  border-left: 3px solid var(--primary-color);
}

.demo-account {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: var(--font-xs);

  &:last-child {
    margin-bottom: 0;
  }
}

.account-label {
  color: var(--text-secondary);
  font-weight: 500;
}

.account-info {
  color: var(--text-primary);
  font-family: monospace;
  background: var(--bg-primary);
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
}

.demo-note {
  color: var(--warning-color);
  font-size: var(--font-xs);
  text-align: center;
  padding: 8px;
  background: rgba(245, 158, 11, 0.1);
  border-radius: 4px;

  i {
    margin-right: 4px;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
