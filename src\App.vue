<template>
  <div id="app">
    <router-view />
    <!-- 注释：移除全局加载组件，改用组件内部状态管理 -->
    <!-- <GlobalLoading /> -->
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'
// 注释：移除全局加载组件导入
// import GlobalLoading from '@/components/GlobalLoading.vue'

export default {
  name: 'App',
  components: {
    // 注释：移除全局加载组件注册
    // GlobalLoading
  },
  computed: {
    ...mapGetters('settings', ['theme', 'fontSize'])
  },
  async created() {
    try {
      // 初始化主题 - 确保主题正确应用
      this.initializeTheme()

      // 加载用户数据
      if (this.$store.getters['user/isLoggedIn']) {
        await this.loadConversations()
      }
    } catch (error) {
      console.error('应用初始化失败:', error)
    }
  },

  errorCaptured(err, vm, info) {
    console.error('Vue错误捕获:', err, info)
    return false
  },
  methods: {
    ...mapActions('chat', ['loadConversations']),

    initializeTheme() {
      // 确保主题正确初始化
      const theme = this.theme || 'light'
      const fontSize = this.fontSize || 'medium'

      document.documentElement.setAttribute('data-theme', theme)
      document.documentElement.setAttribute('data-font-size', fontSize)

      // 如果localStorage中没有主题设置，设置默认值
      if (!localStorage.getItem('theme')) {
        this.$store.commit('settings/SET_THEME', theme)
      }
      if (!localStorage.getItem('fontSize')) {
        this.$store.commit('settings/SET_FONT_SIZE', fontSize)
      }
    }
  },

  watch: {
    // 监听主题变化
    theme: {
      immediate: true,
      handler(newTheme) {
        if (newTheme) {
          document.documentElement.setAttribute('data-theme', newTheme)
        }
      }
    },
    fontSize: {
      immediate: true,
      handler(newFontSize) {
        if (newFontSize) {
          document.documentElement.setAttribute('data-font-size', newFontSize)
        }
      }
    }
  }
}
</script>

<style>
/* 全局样式已在 global.scss 中定义 */
</style>
