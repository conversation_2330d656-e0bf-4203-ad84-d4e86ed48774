const state = {
  theme: localStorage.getItem('theme') || 'light',
  fontSize: localStorage.getItem('fontSize') || 'medium',
  language: localStorage.getItem('language') || 'zh-CN',
  apiKey: localStorage.getItem('apiKey') || '',
  aiModel: localStorage.getItem('aiModel') || 'gpt-4',
  autoSave: localStorage.getItem('autoSave') === 'true',
  dataEncryption: localStorage.getItem('dataEncryption') === 'true'
}

const mutations = {
  SET_THEME(state, theme) {
    state.theme = theme
    localStorage.setItem('theme', theme)
    document.documentElement.setAttribute('data-theme', theme)
  },
  
  SET_FONT_SIZE(state, fontSize) {
    state.fontSize = fontSize
    localStorage.setItem('fontSize', fontSize)
    document.documentElement.setAttribute('data-font-size', fontSize)
  },
  
  SET_LANGUAGE(state, language) {
    state.language = language
    localStorage.setItem('language', language)
  },
  
  SET_API_KEY(state, apiKey) {
    state.apiKey = apiKey
    localStorage.setItem('apiKey', apiKey)
  },
  
  SET_AI_MODEL(state, model) {
    state.aiModel = model
    localStorage.setItem('aiModel', model)
  },
  
  SET_AUTO_SAVE(state, autoSave) {
    state.autoSave = autoSave
    localStorage.setItem('autoSave', autoSave.toString())
  },
  
  SET_DATA_ENCRYPTION(state, encryption) {
    state.dataEncryption = encryption
    localStorage.setItem('dataEncryption', encryption.toString())
  }
}

const actions = {
  updateSettings({ commit }, settings) {
    Object.keys(settings).forEach(key => {
      // 手动映射设置键到对应的mutation
      const mutationMap = {
        'theme': 'SET_THEME',
        'fontSize': 'SET_FONT_SIZE',
        'language': 'SET_LANGUAGE',
        'apiKey': 'SET_API_KEY',
        'aiModel': 'SET_AI_MODEL',
        'autoSave': 'SET_AUTO_SAVE',
        'dataEncryption': 'SET_DATA_ENCRYPTION'
      }

      const mutationName = mutationMap[key]
      if (mutationName && mutations[mutationName]) {
        commit(mutationName, settings[key])
      }
    })
  },
  
  resetSettings({ commit }) {
    commit('SET_THEME', 'light')
    commit('SET_FONT_SIZE', 'medium')
    commit('SET_LANGUAGE', 'zh-CN')
    commit('SET_API_KEY', '')
    commit('SET_AI_MODEL', 'gpt-4')
    commit('SET_AUTO_SAVE', true)
    commit('SET_DATA_ENCRYPTION', true)
  },
  
  clearAllData({ commit }) {
    // 清除聊天数据
    localStorage.removeItem('conversations')
    localStorage.removeItem('messages')
    
    // 可以选择是否清除设置
    const confirmClearSettings = confirm('是否同时清除所有设置？')
    if (confirmClearSettings) {
      localStorage.clear()
      commit('SET_THEME', 'light')
      commit('SET_FONT_SIZE', 'medium')
      commit('SET_LANGUAGE', 'zh-CN')
      commit('SET_API_KEY', '')
      commit('SET_AI_MODEL', 'gpt-4')
      commit('SET_AUTO_SAVE', true)
      commit('SET_DATA_ENCRYPTION', true)
    }
  }
}

const getters = {
  theme: state => state.theme,
  fontSize: state => state.fontSize,
  language: state => state.language,
  apiKey: state => state.apiKey,
  aiModel: state => state.aiModel,
  autoSave: state => state.autoSave,
  dataEncryption: state => state.dataEncryption,
  allSettings: state => state
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
