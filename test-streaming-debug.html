<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>流式 Markdown 调试测试</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }

        .section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .control-buttons {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }

        .control-button {
            padding: 8px 16px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
        }

        .control-button.primary {
            background: #409eff;
            color: white;
        }

        .control-button.primary:hover {
            background: #337ecc;
        }

        .control-button.danger {
            background: #f56c6c;
            color: white;
        }

        .control-button.danger:hover {
            background: #f45656;
        }

        .control-button:disabled {
            background: #c0c4cc;
            cursor: not-allowed;
        }

        .status-info {
            padding: 15px;
            background: #f0f9ff;
            border: 1px solid #409eff;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 12px;
        }

        .status-info h3 {
            color: #409eff;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .rendered-output {
            min-height: 300px;
            max-height: 500px;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 13px;
            line-height: 1.6;
        }

        .debug-output {
            min-height: 200px;
            max-height: 400px;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: #f8f9fa;
            overflow-y: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 11px;
            white-space: pre-wrap;
        }

        .streaming-indicator {
            display: inline-block;
            margin-left: 5px;
            color: #409eff;
            animation: blink 1s infinite;
        }

        @keyframes blink {
            0%, 50% { opacity: 1; }
            51%, 100% { opacity: 0; }
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3 {
            color: #333;
            margin: 16px 0 8px 0;
            font-weight: 600;
        }

        .rendered-output h1 {
            font-size: 1.6em;
            border-bottom: 2px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h2 {
            font-size: 1.4em;
            border-bottom: 1px solid #eee;
            padding-bottom: 4px;
        }

        .rendered-output h3 {
            font-size: 1.2em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 10px 0;
            line-height: 1.6;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 10px 0;
            padding-left: 18px;
        }

        .rendered-output li {
            margin: 4px 0;
            line-height: 1.5;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 12px;
            margin: 12px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.85em;
        }

        .rendered-output blockquote {
            margin: 12px 0;
            padding: 8px 12px;
            border-left: 3px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 6px 6px 0;
            font-style: italic;
            color: #666;
        }

        @media (max-width: 1200px) {
            .test-container {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>流式 Markdown 渲染调试测试</h1>
                <p>深度调试流式输出问题</p>
            </div>

            <div class="test-container">
                <!-- 控制面板 -->
                <div class="section">
                    <div class="section-title">控制面板</div>
                    
                    <div class="control-buttons">
                        <button class="control-button primary" @click="startStreaming" :disabled="isStreaming">
                            开始流式
                        </button>
                        <button class="control-button danger" @click="stopStreaming" :disabled="!isStreaming">
                            停止
                        </button>
                    </div>

                    <div class="status-info">
                        <h3>状态信息</h3>
                        <div>流式状态: {{ streamStatus }}</div>
                        <div>进度: {{ progress }}%</div>
                        <div>字符数: {{ currentContent.length }}</div>
                        <div>渲染次数: {{ renderCount }}</div>
                        <div>更新次数: {{ updateCount }}</div>
                    </div>

                    <div class="control-buttons">
                        <button class="control-button primary" @click="loadExample('simple')">简单示例</button>
                        <button class="control-button primary" @click="loadExample('complex')">复杂示例</button>
                        <button class="control-button primary" @click="testSpeed('fast')">快速测试</button>
                        <button class="control-button primary" @click="testSpeed('slow')">慢速测试</button>
                    </div>

                    <div class="debug-output">{{ debugLog }}</div>
                </div>

                <!-- 渲染结果 -->
                <div class="section">
                    <div class="section-title">
                        渲染结果
                        <span v-if="isStreaming" class="streaming-indicator">●</span>
                    </div>
                    <div class="rendered-output" v-html="renderedHtml"></div>
                </div>

                <!-- 原始内容 -->
                <div class="section">
                    <div class="section-title">原始内容</div>
                    <div class="debug-output">{{ currentContent }}</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                currentContent: '',
                renderedHtml: '',
                md: null,
                isStreaming: false,
                streamTimer: null,
                currentExample: '',
                currentIndex: 0,
                renderCount: 0,
                updateCount: 0,
                streamSpeed: 50,
                debugLog: '',
                examples: {
                    simple: `# 简单测试

这是一个**简单**的测试。

- 列表项 1
- 列表项 2

\`代码示例\``,
                    complex: `# Java的核心特点

## 跨平台性 ("Write Once, Run Anywhere")

• Java程序通过编译生成**字节码** (bytecode)，运行在 Java虚拟机 (JVM) 上。
- JVM 是平台相关的，但 Java程序本身是平台无关的，因此可以在任何安装了 JVM 的设备上运行。

### 面向对象编程

Java是一种**面向对象**的编程语言：

\`\`\`java
public class HelloWorld {
    public static void main(String[] args) {
        System.out.println("Hello, World!");
    }
}
\`\`\`

> Java的设计哲学是"简单、健壮、可移植"`
                }
            },
            computed: {
                streamStatus() {
                    return this.isStreaming ? '流式输出中' : '未开始'
                },
                progress() {
                    if (!this.currentExample) return 0
                    return Math.round((this.currentIndex / this.currentExample.length) * 100)
                }
            },
            mounted() {
                this.initMarkdown()
                this.loadExample('simple')
                this.addDebugLog('应用初始化完成')
            },
            methods: {
                addDebugLog(message) {
                    const timestamp = new Date().toLocaleTimeString()
                    this.debugLog += `[${timestamp}] ${message}\n`
                    // 保持日志在合理长度
                    const lines = this.debugLog.split('\n')
                    if (lines.length > 50) {
                        this.debugLog = lines.slice(-50).join('\n')
                    }
                },

                // 初始化 Markdown-it
                initMarkdown() {
                    if (window.markdownit) {
                        this.md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            linkify: true,
                            typographer: true
                        })
                        this.addDebugLog('✅ Markdown-it 初始化成功')
                    } else {
                        this.addDebugLog('❌ markdown-it 库未加载')
                    }
                },
                
                // 更新输出
                updateOutput() {
                    this.updateCount++
                    this.addDebugLog(`🔄 updateOutput 调用 #${this.updateCount}`)
                    
                    if (!this.md) {
                        this.renderedHtml = '<p style="color: red;">Markdown-it 库未加载</p>'
                        this.addDebugLog('⚠️ Markdown-it 实例不存在')
                        return
                    }
                    
                    if (!this.currentContent) {
                        this.renderedHtml = ''
                        return
                    }
                    
                    try {
                        const startTime = Date.now()
                        this.renderedHtml = this.md.render(this.currentContent)
                        const renderTime = Date.now() - startTime
                        this.renderCount++
                        
                        this.addDebugLog(`✅ 渲染成功: ${this.currentContent.length} 字符, 耗时 ${renderTime}ms, 第${this.renderCount}次`)
                        
                    } catch (error) {
                        this.addDebugLog(`❌ 渲染失败: ${error.message}`)
                        this.renderedHtml = `<p style="color: red;">渲染错误: ${error.message}</p>`
                    }
                },
                
                startStreaming() {
                    if (!this.currentExample) {
                        this.loadExample('simple')
                    }
                    
                    this.isStreaming = true
                    this.currentIndex = 0
                    this.currentContent = ''
                    this.renderCount = 0
                    this.updateCount = 0
                    this.debugLog = ''
                    
                    this.addDebugLog('🚀 开始流式输出')
                    
                    this.streamTimer = setInterval(() => {
                        if (this.currentIndex < this.currentExample.length) {
                            // 模拟流式输出，每次添加1-3个字符
                            const chunkSize = Math.floor(Math.random() * 3) + 1
                            const endIndex = Math.min(this.currentIndex + chunkSize, this.currentExample.length)
                            
                            this.currentContent = this.currentExample.substring(0, endIndex)
                            this.currentIndex = endIndex
                            
                            this.addDebugLog(`📝 内容更新: ${this.currentContent.length} 字符`)
                            this.updateOutput()
                            
                            if (this.currentIndex >= this.currentExample.length) {
                                this.stopStreaming()
                            }
                        }
                    }, this.streamSpeed)
                },
                
                stopStreaming() {
                    this.isStreaming = false
                    if (this.streamTimer) {
                        clearInterval(this.streamTimer)
                        this.streamTimer = null
                    }
                    
                    this.addDebugLog('🛑 流式输出停止')
                    
                    // 流式完成后，确保最终渲染是正确的
                    this.$nextTick(() => {
                        this.updateOutput()
                        this.addDebugLog('🔄 最终渲染完成')
                    })
                },
                
                loadExample(type) {
                    this.stopStreaming()
                    this.currentExample = this.examples[type] || this.examples.simple
                    this.currentContent = this.currentExample
                    this.currentIndex = this.currentExample.length
                    this.addDebugLog(`📋 加载示例: ${type}`)
                    this.updateOutput()
                },
                
                testSpeed(speed) {
                    this.stopStreaming()
                    this.streamSpeed = speed === 'fast' ? 20 : 100
                    this.addDebugLog(`⚡ 设置速度: ${speed} (${this.streamSpeed}ms)`)
                    this.startStreaming()
                }
            },
            
            beforeDestroy() {
                this.stopStreaming()
            }
        })
    </script>
</body>
</html>
