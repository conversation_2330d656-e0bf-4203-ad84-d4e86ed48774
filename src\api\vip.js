import request from '@/utils/request'

/**
 * 获取VIP信息
 */
export function getVipInfo() {
  return request({
    url: '/vip/info',
    method: 'get'
  })
}

/**
 * 获取VIP套餐列表
 */
export function getVipPlans() {
  return request({
    url: '/vip/plans',
    method: 'get'
  })
}

/**
 * 购买VIP
 * @param {Object} data - 购买数据
 * @param {string} data.planId - 套餐ID
 * @param {string} data.duration - 购买时长 ('monthly' | 'yearly')
 * @param {string} data.paymentMethod - 支付方式
 */
export function purchaseVip(data) {
  return request({
    url: '/vip/purchase',
    method: 'post',
    data
  })
}

/**
 * 获取账户余额
 */
export function getBalance() {
  return request({
    url: '/account/balance',
    method: 'get'
  })
}

/**
 * 充值
 * @param {Object} data - 充值数据
 * @param {number} data.amount - 充值金额（分）
 * @param {string} data.paymentMethod - 支付方式
 */
export function recharge(data) {
  return request({
    url: '/account/recharge',
    method: 'post',
    data
  })
}

/**
 * 获取消费记录
 * @param {Object} params - 查询参数
 * @param {number} params.page - 页码
 * @param {number} params.limit - 每页数量
 * @param {string} params.type - 记录类型 ('recharge' | 'consume' | 'all')
 */
export function getTransactionHistory(params) {
  return request({
    url: '/account/transactions',
    method: 'get',
    params
  })
}

/**
 * 获取使用统计
 */
export function getUsageStats() {
  return request({
    url: '/account/usage-stats',
    method: 'get'
  })
}

/**
 * 创建支付订单
 * @param {Object} data - 订单数据
 * @param {string} data.type - 订单类型 ('vip' | 'recharge')
 * @param {number} data.amount - 金额
 * @param {string} data.paymentMethod - 支付方式
 */
export function createPaymentOrder(data) {
  return request({
    url: '/payment/create-order',
    method: 'post',
    data
  })
}

/**
 * 查询支付状态
 * @param {string} orderId - 订单ID
 */
export function getPaymentStatus(orderId) {
  return request({
    url: `/payment/status/${orderId}`,
    method: 'get'
  })
}

/**
 * 取消订单
 * @param {string} orderId - 订单ID
 */
export function cancelOrder(orderId) {
  return request({
    url: `/payment/cancel/${orderId}`,
    method: 'post'
  })
}

/**
 * 获取支付方式列表
 */
export function getPaymentMethods() {
  return request({
    url: '/vip/paymentMethodList',
    method: 'get'
  })
}


/**
 * 获取充值列表相关信息
 */
export function getRechargeList() {
  return request({
    url: '/vip/getRechargeList',
    method: 'get'
  })
}


/**
 * 获取测试支付宝扫码支付预付款支付页面
 */
export function getAliPayCode() {
  return request({
    url: '/vip/aliPayCode',
    method: 'get'
  })
}