<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown 解析器测试 - vue-markdown-render 和 v-markdown 指令</title>
    <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
    <script src="https://unpkg.com/markdown-it@13.0.1/dist/markdown-it.min.js"></script>
    <script src="https://unpkg.com/markdown-it-anchor@8.6.7/dist/markdownItAnchor.umd.js"></script>
    <script src="https://unpkg.com/markdown-it-task-lists@2.1.1/dist/index.js"></script>
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.8.0/highlight.min.js"></script>
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.8.0/languages/javascript.min.js"></script>
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.8.0/languages/python.min.js"></script>
    <script src="https://unpkg.com/@highlightjs/cdn-assets@11.8.0/languages/java.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/@highlightjs/cdn-assets@11.8.0/styles/github.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 30px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #409eff;
            font-size: 2.2em;
            margin-bottom: 10px;
        }

        .header p {
            color: #666;
            font-size: 1.1em;
        }

        .test-tabs {
            background: white;
            border-radius: 12px;
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        .tab-buttons {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
        }

        .tab-button {
            flex: 1;
            padding: 15px 20px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            color: #666;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            color: #409eff;
            border-bottom: 3px solid #409eff;
        }

        .tab-button:hover {
            background: #e9ecef;
        }

        .tab-content {
            padding: 30px;
        }

        .test-section {
            display: none;
        }

        .test-section.active {
            display: block;
        }

        .test-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            height: 600px;
        }

        .input-section, .output-section {
            display: flex;
            flex-direction: column;
        }

        .section-title {
            font-size: 1.2em;
            font-weight: 600;
            color: #409eff;
            margin-bottom: 15px;
        }

        .markdown-input {
            flex: 1;
            padding: 15px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .markdown-input:focus {
            border-color: #409eff;
        }

        .rendered-output {
            flex: 1;
            padding: 20px;
            border: 1px solid #dcdfe6;
            border-radius: 8px;
            background: white;
            overflow-y: auto;
            font-size: 14px;
            line-height: 1.6;
        }

        .example-buttons {
            margin-top: 20px;
            text-align: center;
        }

        .example-button {
            margin: 5px;
            padding: 10px 20px;
            background: #409eff;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s ease;
        }

        .example-button:hover {
            background: #337ecc;
        }

        /* Markdown 样式 */
        .rendered-output h1,
        .rendered-output h2,
        .rendered-output h3,
        .rendered-output h4,
        .rendered-output h5,
        .rendered-output h6 {
            color: #333;
            margin: 20px 0 12px 0;
            font-weight: 600;
            line-height: 1.4;
        }

        .rendered-output h1 {
            font-size: 1.8em;
            border-bottom: 2px solid #eee;
            padding-bottom: 8px;
        }

        .rendered-output h2 {
            font-size: 1.5em;
            border-bottom: 1px solid #eee;
            padding-bottom: 6px;
        }

        .rendered-output h3 {
            font-size: 1.3em;
            color: #409eff;
        }

        .rendered-output p {
            margin: 14px 0;
            line-height: 1.7;
        }

        .rendered-output ul,
        .rendered-output ol {
            margin: 12px 0;
            padding-left: 20px;
        }

        .rendered-output li {
            margin: 6px 0;
            line-height: 1.6;
        }

        .rendered-output strong {
            font-weight: 700;
            color: #409eff;
        }

        .rendered-output em {
            font-style: italic;
            color: #666;
        }

        .rendered-output del {
            text-decoration: line-through;
            color: #999;
            opacity: 0.7;
        }

        .rendered-output code {
            background: rgba(64, 158, 255, 0.1);
            color: #409eff;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
            border: 1px solid rgba(64, 158, 255, 0.2);
        }

        .rendered-output pre {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 16px;
            margin: 16px 0;
            overflow-x: auto;
            font-family: 'Courier New', Monaco, monospace;
            font-size: 0.9em;
            line-height: 1.5;
        }

        .rendered-output pre code {
            background: none;
            border: none;
            padding: 0;
            color: inherit;
        }

        .rendered-output blockquote {
            margin: 16px 0;
            padding: 12px 16px;
            border-left: 4px solid #409eff;
            background: rgba(64, 158, 255, 0.05);
            border-radius: 0 8px 8px 0;
            font-style: italic;
            color: #666;
        }

        .rendered-output table {
            width: 100%;
            border-collapse: collapse;
            margin: 16px 0;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            overflow: hidden;
        }

        .rendered-output th,
        .rendered-output td {
            padding: 12px 16px;
            border-bottom: 1px solid #e9ecef;
            text-align: left;
        }

        .rendered-output th {
            background: rgba(64, 158, 255, 0.1);
            font-weight: 600;
            color: #409eff;
        }

        .rendered-output tr:nth-child(even) {
            background: rgba(64, 158, 255, 0.02);
        }

        .rendered-output a {
            color: #409eff;
            text-decoration: none;
            border-bottom: 1px solid transparent;
            transition: all 0.2s ease;
        }

        .rendered-output a:hover {
            border-bottom-color: #409eff;
        }

        .rendered-output img {
            max-width: 100%;
            height: auto;
            border-radius: 8px;
            margin: 12px 0;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .rendered-output hr {
            margin: 24px 0;
            border: none;
            height: 2px;
            background: linear-gradient(to right, transparent, #e9ecef, transparent);
        }

        /* 任务列表样式 */
        .rendered-output .task-list-item {
            display: flex;
            align-items: flex-start;
            margin: 8px 0;
            list-style: none;
            padding-left: 0;
        }

        .rendered-output .task-list-item-checkbox {
            margin-right: 8px;
            margin-top: 2px;
            cursor: default;
        }

        .rendered-output .task-list-item-checkbox:checked + * {
            text-decoration: line-through;
            color: #999;
            opacity: 0.7;
        }

        @media (max-width: 768px) {
            .test-container {
                grid-template-columns: 1fr;
                height: auto;
                gap: 15px;
            }

            .rendered-output {
                min-height: 300px;
            }

            .tab-buttons {
                flex-direction: column;
            }

            .container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1>Markdown 解析器测试页面</h1>
                <p>测试不同的 Markdown 解析方式：自定义组件和 v-markdown 指令</p>
            </div>

            <div class="test-tabs">
                <div class="tab-buttons">
                    <button 
                        class="tab-button" 
                        :class="{ active: activeTab === 'component' }"
                        @click="activeTab = 'component'"
                    >
                        自定义 Markdown 组件
                    </button>
                    <button 
                        class="tab-button" 
                        :class="{ active: activeTab === 'directive' }"
                        @click="activeTab = 'directive'"
                    >
                        v-markdown 指令
                    </button>
                    <button 
                        class="tab-button" 
                        :class="{ active: activeTab === 'comparison' }"
                        @click="activeTab = 'comparison'"
                    >
                        对比测试
                    </button>
                </div>

                <div class="tab-content">
                    <!-- 自定义组件测试 -->
                    <div class="test-section" :class="{ active: activeTab === 'component' }">
                        <div class="test-container">
                            <div class="input-section">
                                <div class="section-title">输入 Markdown：</div>
                                <textarea 
                                    class="markdown-input" 
                                    v-model="componentContent"
                                    placeholder="在这里输入 Markdown 内容..."
                                ></textarea>
                            </div>
                            <div class="output-section">
                                <div class="section-title">渲染结果（自定义组件）：</div>
                                <div class="rendered-output">
                                    <markdown-component :source="componentContent"></markdown-component>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- v-markdown 指令测试 -->
                    <div class="test-section" :class="{ active: activeTab === 'directive' }">
                        <div class="test-container">
                            <div class="input-section">
                                <div class="section-title">输入 Markdown：</div>
                                <textarea 
                                    class="markdown-input" 
                                    v-model="directiveContent"
                                    placeholder="在这里输入 Markdown 内容..."
                                ></textarea>
                            </div>
                            <div class="output-section">
                                <div class="section-title">渲染结果（v-markdown 指令）：</div>
                                <div class="rendered-output" v-markdown="directiveContent"></div>
                            </div>
                        </div>
                    </div>

                    <!-- 对比测试 -->
                    <div class="test-section" :class="{ active: activeTab === 'comparison' }">
                        <div class="test-container">
                            <div class="input-section">
                                <div class="section-title">输入 Markdown：</div>
                                <textarea 
                                    class="markdown-input" 
                                    v-model="comparisonContent"
                                    placeholder="在这里输入 Markdown 内容进行对比..."
                                ></textarea>
                            </div>
                            <div class="output-section">
                                <div class="section-title">对比结果：</div>
                                <div style="display: grid; grid-template-rows: 1fr 1fr; gap: 10px; height: 100%;">
                                    <div>
                                        <div style="font-weight: 600; margin-bottom: 5px; color: #409eff;">自定义组件：</div>
                                        <div class="rendered-output" style="height: calc(100% - 25px);">
                                            <markdown-component :source="comparisonContent"></markdown-component>
                                        </div>
                                    </div>
                                    <div>
                                        <div style="font-weight: 600; margin-bottom: 5px; color: #67c23a;">v-markdown 指令：</div>
                                        <div class="rendered-output" style="height: calc(100% - 25px);" v-markdown="comparisonContent"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="example-buttons">
                    <button class="example-button" @click="loadExample('basic')">基础语法</button>
                    <button class="example-button" @click="loadExample('advanced')">高级功能</button>
                    <button class="example-button" @click="loadExample('code')">代码高亮</button>
                    <button class="example-button" @click="loadExample('table')">表格和任务</button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // highlight.js 已经通过 CDN 加载，语言包也已自动注册

        // 自定义 Markdown 组件
        Vue.component('markdown-component', {
            props: {
                source: {
                    type: String,
                    default: ''
                }
            },
            data() {
                return {
                    md: null
                }
            },
            created() {
                this.initMarkdownIt()
            },
            computed: {
                renderedHtml() {
                    if (!this.source || !this.md) return ''
                    try {
                        return this.md.render(this.source)
                    } catch (error) {
                        console.error('Markdown渲染失败:', error)
                        return this.escapeHtml(this.source)
                    }
                }
            },
            methods: {
                initMarkdownIt() {
                    this.md = window.markdownit({
                        html: true,
                        xhtmlOut: false,
                        breaks: true,
                        langPrefix: 'language-',
                        linkify: true,
                        typographer: true,
                        highlight: function (str, lang) {
                            if (lang && hljs.getLanguage(lang)) {
                                try {
                                    return hljs.highlight(str, { language: lang }).value;
                                } catch (__) {}
                            }
                            return '';
                        }
                    })

                    // 添加插件
                    if (window.markdownItAnchor) {
                        this.md.use(window.markdownItAnchor.default || window.markdownItAnchor, {
                            permalink: false
                        })
                    }

                    if (window.markdownItTaskLists) {
                        this.md.use(window.markdownItTaskLists)
                    }
                },
                escapeHtml(text) {
                    const map = {
                        '&': '&amp;',
                        '<': '&lt;',
                        '>': '&gt;',
                        '"': '&quot;',
                        "'": '&#039;'
                    }
                    return text.replace(/[&<>"']/g, (m) => map[m])
                }
            },
            template: '<div v-html="renderedHtml"></div>'
        })

        // Vue 应用
        new Vue({
            el: '#app',
            data: {
                activeTab: 'component',
                componentContent: '',
                directiveContent: '',
                comparisonContent: '',
                examples: {
                    basic: `# 基础 Markdown 语法测试

## 文本格式

这是一段普通文本，包含 **粗体文本** 和 *斜体文本*，还有 ~~删除线文本~~。

这里有一些行内代码：\`const hello = "world";\`

## 列表

### 无序列表
- 项目 1
- 项目 2
  - 子项目 2.1
  - 子项目 2.2
- 项目 3

### 有序列表
1. 第一项
2. 第二项
3. 第三项

## 链接和图片

[Vue.js 官网](https://vuejs.org)

![示例图片](https://via.placeholder.com/300x200/42b883/ffffff?text=Vue.js)

## 引用

> 这是一个引用块
>
> 可以包含多行内容

---

这是分隔线后的内容。`,
                    advanced: `# 高级 Markdown 功能

## 任务列表

- [x] 已完成的任务
- [ ] 未完成的任务
- [x] 另一个已完成的任务
- [ ] 待办事项

## 表格

| 功能 | 自定义组件 | v-markdown 指令 | 原有组件 |
|------|-----------|----------------|----------|
| 基础语法 | ✅ | ✅ | ✅ |
| 代码高亮 | ✅ | ✅ | ✅ |
| 任务列表 | ✅ | ✅ | ✅ |
| 表格支持 | ✅ | ✅ | ✅ |
| 自定义样式 | ✅ | ✅ | ✅ |

## 嵌套引用

> 第一级引用
>
> > 第二级引用
> >
> > > 第三级引用

## 复杂列表

1. 第一级有序列表
   - 第二级无序列表
   - 另一个第二级项目
     1. 第三级有序列表
     2. 另一个第三级项目
        - [x] 第四级任务列表（已完成）
        - [ ] 第四级任务列表（未完成）
2. 回到第一级
3. 最后一个第一级项目`,
                    code: `# 代码高亮测试

## JavaScript 代码

\`\`\`javascript
// Vue 组件示例
export default {
  name: 'MarkdownTest',
  data() {
    return {
      message: 'Hello, Markdown!'
    }
  },
  methods: {
    renderMarkdown(content) {
      return this.markdownIt.render(content)
    }
  }
}
\`\`\`

## Python 代码

\`\`\`python
# Python 示例
def fibonacci(n):
    """计算斐波那契数列"""
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

# 测试
for i in range(10):
    print(f"fibonacci({i}) = {fibonacci(i)}")
\`\`\`

## Java 代码

\`\`\`java
// Java 示例
public class MarkdownTest {
    public static void main(String[] args) {
        System.out.println("Hello, Markdown!");

        // 创建列表
        List<String> items = Arrays.asList(
            "自定义组件",
            "v-markdown 指令",
            "原有组件"
        );

        items.forEach(System.out::println);
    }
}
\`\`\``,
                    table: `# 表格和任务列表测试

## 功能对比表格

| 解析器 | 性能 | 易用性 | 自定义性 | Vue 2 兼容 | 推荐度 |
|--------|------|--------|----------|------------|--------|
| 自定义组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐⭐ |
| v-markdown 指令 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐⭐ |
| 原有组件 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ✅ | ⭐⭐⭐⭐ |

## 项目任务清单

### 开发任务
- [x] 创建测试页面
- [x] 实现自定义组件
- [x] 实现 v-markdown 指令
- [ ] 添加更多示例
- [ ] 优化样式
- [ ] 编写文档

### 测试任务
- [x] 基础语法测试
- [x] 代码高亮测试
- [x] 表格渲染测试
- [ ] 性能测试
- [ ] 兼容性测试
- [ ] 用户体验测试

### 部署任务
- [ ] 代码审查
- [ ] 单元测试
- [ ] 集成测试
- [ ] 生产环境部署`
                }
            },
            mounted() {
                // 初始化内容
                this.loadExample('basic')
            },
            methods: {
                loadExample(type) {
                    const content = this.examples[type] || this.examples.basic
                    this.componentContent = content
                    this.directiveContent = content
                    this.comparisonContent = content
                }
            },
            // 注册 v-markdown 指令
            directives: {
                markdown: {
                    bind(el, binding) {
                        const md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            langPrefix: 'language-',
                            linkify: true,
                            typographer: true,
                            highlight: function (str, lang) {
                                if (lang && hljs.getLanguage(lang)) {
                                    try {
                                        return hljs.highlight(str, { language: lang }).value;
                                    } catch (__) {}
                                }
                                return '';
                            }
                        })

                        // 添加插件
                        if (window.markdownItAnchor) {
                            md.use(window.markdownItAnchor.default || window.markdownItAnchor, {
                                permalink: false
                            })
                        }

                        if (window.markdownItTaskLists) {
                            md.use(window.markdownItTaskLists)
                        }

                        if (binding.value) {
                            el.innerHTML = md.render(binding.value)
                        }
                    },
                    update(el, binding) {
                        const md = window.markdownit({
                            html: true,
                            xhtmlOut: false,
                            breaks: true,
                            langPrefix: 'language-',
                            linkify: true,
                            typographer: true,
                            highlight: function (str, lang) {
                                if (lang && hljs.getLanguage(lang)) {
                                    try {
                                        return hljs.highlight(str, { language: lang }).value;
                                    } catch (__) {}
                                }
                                return '';
                            }
                        })

                        // 添加插件
                        if (window.markdownItAnchor) {
                            md.use(window.markdownItAnchor.default || window.markdownItAnchor, {
                                permalink: false
                            })
                        }

                        if (window.markdownItTaskLists) {
                            md.use(window.markdownItTaskLists)
                        }

                        if (binding.value) {
                            el.innerHTML = md.render(binding.value)
                        }
                    }
                }
            }
        })
    </script>
</body>
</html>
