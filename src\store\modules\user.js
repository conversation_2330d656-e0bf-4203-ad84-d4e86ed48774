const state = {
  userInfo: {
    id: '',
    username: '',
    email: '',
    avatar: '',
    bio: '',
    // VIP相关字段
    vipLevel: 'free', // 'free', 'vip', 'super_vip'
    vipExpireTime: null, // VIP到期时间
    balance: 0, // 账户余额（分为单位）
    totalSpent: 0, // 累计消费金额
    dailyUsage: {
      date: new Date().toDateString(),
      messageCount: 0, // 今日消息数量
      modelUsage: {} // 各模型使用次数
    }
  },
  token: localStorage.getItem('token') || '',
  isLoggedIn: false,
  // VIP相关状态
  vipPlans: [
    {
      id: 'free',
      name: '免费用户',
      price: 0,
      duration: 0,
      features: [
        '每日20次对话',
        '基础AI模型',
        '消息保存7天',
        '标准响应速度'
      ],
      limits: {
        dailyMessages: 20,
        models: ['basic'],
        historyDays: 7
      }
    },
    {
      id: 'vip',
      name: 'VIP会员',
      monthlyPrice: 2900, // 29元，以分为单位
      yearlyPrice: 29900, // 299元
      features: [
        '每日200次对话',
        '基础+高级AI模型',
        '消息保存30天',
        '优先响应速度',
        '文件上传功能'
      ],
      limits: {
        dailyMessages: 200,
        models: ['basic', 'advanced'],
        historyDays: 30
      }
    },
    {
      id: 'super_vip',
      name: '超级VIP',
      monthlyPrice: 9900, // 99元
      yearlyPrice: 99900, // 999元
      features: [
        '无限制对话',
        '全部AI模型',
        '永久消息保存',
        '最高优先级',
        '专属客服',
        'API访问权限'
      ],
      limits: {
        dailyMessages: -1, // -1表示无限制
        models: ['basic', 'advanced', 'premium'],
        historyDays: -1 // -1表示永久保存
      }
    }
  ]
}

const mutations = {
  SET_USER_INFO(state, userInfo) {
    state.userInfo = { ...state.userInfo, ...userInfo }
  },
  SET_TOKEN(state, token) {
    state.token = token
    state.isLoggedIn = !!token
    if (token) {
      localStorage.setItem('token', token)
    } else {
      localStorage.removeItem('token')
    }
  },
  CLEAR_USER_DATA(state) {
    state.userInfo = {
      id: '',
      username: '',
      email: '',
      avatar: '',
      bio: '',
      vipLevel: 'free',
      vipExpireTime: null,
      balance: 0,
      totalSpent: 0,
      dailyUsage: {
        date: new Date().toDateString(),
        messageCount: 0,
        modelUsage: {}
      }
    }
    state.token = ''
    state.isLoggedIn = false
    localStorage.removeItem('token')
  },

  // VIP相关mutations
  SET_VIP_INFO(state, vipInfo) {
    state.userInfo.vipLevel = vipInfo.vipLevel || state.userInfo.vipLevel
    state.userInfo.vipExpireTime = vipInfo.vipExpireTime || state.userInfo.vipExpireTime
    state.userInfo.balance = vipInfo.balance !== undefined ? vipInfo.balance : state.userInfo.balance
    state.userInfo.totalSpent = vipInfo.totalSpent !== undefined ? vipInfo.totalSpent : state.userInfo.totalSpent
  },

  UPDATE_BALANCE(state, amount) {
    state.userInfo.balance += amount
  },

  UPDATE_DAILY_USAGE(state, { messageCount = 0, model = null }) {
    const today = new Date().toDateString()

    // 如果是新的一天，重置使用量
    if (state.userInfo.dailyUsage.date !== today) {
      state.userInfo.dailyUsage = {
        date: today,
        messageCount: 0,
        modelUsage: {}
      }
    }

    // 更新消息数量
    state.userInfo.dailyUsage.messageCount += messageCount

    // 更新模型使用次数
    if (model) {
      state.userInfo.dailyUsage.modelUsage[model] =
        (state.userInfo.dailyUsage.modelUsage[model] || 0) + 1
    }
  },

  UPGRADE_VIP(state, { vipLevel, expireTime }) {
    state.userInfo.vipLevel = vipLevel
    state.userInfo.vipExpireTime = expireTime
  }
}

const actions = {
  // 登录 - 暂时使用模拟登录
  async login({ commit }, { email, password }) {
    try {
      // 模拟登录验证
      if (!email || !password) {
        throw new Error('邮箱和密码不能为空')
      }

      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 简单的模拟验证逻辑
      const validCredentials = [
        { email: '<EMAIL>', password: '123456' },
        { email: '<EMAIL>', password: '123456' },
        { email: '<EMAIL>', password: 'admin123' }
      ]

      const isValid = validCredentials.some(cred =>
        cred.email === email && cred.password === password
      )

      if (!isValid) {
        throw new Error('邮箱或密码错误')
      }

      // 模拟成功响应
      const response = {
        token: 'mock-jwt-token-' + Date.now(),
        user: {
          id: Date.now().toString(),
          username: email.split('@')[0], // 使用邮箱前缀作为用户名
          email: email,
          avatar: '',
          bio: '欢迎使用AI助手！'
        }
      }

      commit('SET_TOKEN', response.token)
      commit('SET_USER_INFO', response.user)

      return { success: true }
    } catch (error) {
      console.error('登录失败:', error)
      return { success: false, message: error.message || '登录失败' }
    }
  },

  // 登出
  async logout({ commit }) {
    try {
      const { logout: logoutAPI } = await import('@/api/user')
      await logoutAPI()
    } catch (error) {
      console.error('登出API调用失败:', error)
    } finally {
      commit('CLEAR_USER_DATA')
    }
  },

  // 获取用户信息 - 暂时使用模拟数据
  async fetchUserInfo({ commit, state }) {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 500))

      // 如果已有用户信息，直接返回
      if (state.userInfo.id) {
        return state.userInfo
      }

      // 模拟用户信息
      const userInfo = {
        id: '1',
        username: '演示用户',
        email: '<EMAIL>',
        avatar: '',
        bio: '这是一个演示账户'
      }

      commit('SET_USER_INFO', userInfo)
      return userInfo
    } catch (error) {
      console.error('获取用户信息失败:', error)
      throw error
    }
  },

  // 更新用户信息 - 暂时使用模拟更新
  async updateUserInfo({ commit, state }, userInfo) {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 800))

      // 合并更新的用户信息
      const updatedInfo = {
        ...state.userInfo,
        ...userInfo,
        updatedAt: new Date().toISOString()
      }

      commit('SET_USER_INFO', updatedInfo)
      return updatedInfo
    } catch (error) {
      console.error('更新用户信息失败:', error)
      throw error
    }
  },

  // 获取用户统计信息 - 暂时使用模拟数据
  async fetchUserStats({ rootState }) {
    try {
      // 模拟网络延迟
      await new Promise(resolve => setTimeout(resolve, 600))

      // 基于实际数据生成统计信息
      const conversations = rootState.chat.conversations || []
      const messages = rootState.chat.messages || {}

      const totalMessages = Object.values(messages).reduce((total, msgs) => total + msgs.length, 0)
      const usageDays = Math.floor((Date.now() - new Date('2024-01-01').getTime()) / (1000 * 60 * 60 * 24))

      return {
        conversationCount: conversations.length,
        messageCount: totalMessages,
        usageDays: Math.max(1, usageDays),
        favoriteCount: Math.floor(conversations.length * 0.3)
      }
    } catch (error) {
      console.error('获取用户统计失败:', error)
      throw error
    }
  },

  // VIP相关actions
  async fetchVipInfo({ commit }) {
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 500))

      // 模拟VIP信息
      const vipInfo = {
        vipLevel: 'free',
        vipExpireTime: null,
        balance: 0,
        totalSpent: 0
      }

      commit('SET_VIP_INFO', vipInfo)
      return vipInfo
    } catch (error) {
      console.error('获取VIP信息失败:', error)
      throw error
    }
  },



  async purchaseVip({ commit }, { planId, duration, paymentMethod }) {
    try {
      // 模拟支付处理
      await new Promise(resolve => setTimeout(resolve, 2000))

      // 计算到期时间
      const now = new Date()
      const expireTime = new Date(now.getTime() + duration * 24 * 60 * 60 * 1000)

      commit('UPGRADE_VIP', {
        vipLevel: planId,
        expireTime: expireTime.toISOString()
      })

      return { success: true, message: 'VIP购买成功' }
    } catch (error) {
      console.error('VIP购买失败:', error)
      return { success: false, message: error.message || 'VIP购买失败' }
    }
  },

  async recharge({ commit }, { amount, paymentMethod }) {
    try {
      // 模拟充值处理
      await new Promise(resolve => setTimeout(resolve, 1500))

      commit('UPDATE_BALANCE', amount)

      return { success: true, message: '充值成功' }
    } catch (error) {
      console.error('充值失败:', error)
      return { success: false, message: error.message || '充值失败' }
    }
  },

  // 检查用户权限
  checkUserPermission({ state }, { action, model }) {
    const userInfo = state.userInfo
    const vipPlan = state.vipPlans.find(plan => plan.id === userInfo.vipLevel)

    if (!vipPlan) return false

    // 检查VIP是否过期
    if (userInfo.vipExpireTime && new Date(userInfo.vipExpireTime) < new Date()) {
      return false
    }

    switch (action) {
      case 'sendMessage':
        // 检查每日消息限制
        if (vipPlan.limits.dailyMessages !== -1 &&
            userInfo.dailyUsage.messageCount >= vipPlan.limits.dailyMessages) {
          return false
        }
        break

      case 'useModel':
        // 检查模型使用权限
        if (!vipPlan.limits.models.includes(model)) {
          return false
        }
        break
    }

    return true
  }
}

const getters = {
  userInfo: state => state.userInfo,
  isLoggedIn: state => state.isLoggedIn,
  token: state => state.token,

  // VIP相关getters
  vipLevel: state => state.userInfo.vipLevel,
  isVip: state => state.userInfo.vipLevel !== 'free',
  vipExpireTime: state => state.userInfo.vipExpireTime,
  balance: state => state.userInfo.balance,
  balanceYuan: state => (state.userInfo.balance / 100).toFixed(2),

  // 获取当前VIP套餐信息
  currentVipPlan: state => {
    return state.vipPlans.find(plan => plan.id === state.userInfo.vipLevel) || state.vipPlans[0] || { name: '未知套餐' }
  },

  // 计算剩余消息数量
  remainingMessages: state => {
    const currentPlan = state.vipPlans.find(plan => plan.id === state.userInfo.vipLevel)
    if (!currentPlan) return 0

    // 如果是无限制，返回-1
    if (currentPlan.limits.dailyMessages === -1) return -1

    // 计算剩余消息数
    const used = state.userInfo.dailyUsage.messageCount || 0
    const remaining = currentPlan.limits.dailyMessages - used
    return Math.max(0, remaining)
  },

  // 检查VIP是否过期
  isVipExpired: state => {
    if (!state.userInfo.vipExpireTime) return false
    return new Date(state.userInfo.vipExpireTime) < new Date()
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
